import { FormSchema } from '/@/components/Form';

// 动态验证规则
export const getLendAreaRule = (type: number) => {
  return {
    required: type === 0 || type === 1,
    message: '请输入借出面积（㎡）',
    trigger: 'blur',
  };
};

export const getEndDateRule = (startDate: string) => {
  return {
    validator: (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      }
      
      if (!startDate) {
        callback(new Error('请先选择借出起始日期'));
        return;
      }
      
      const start = new Date(startDate);
      const end = new Date(value);
      
      if (end <= start) {
        callback(new Error('借出结束日期必须大于借出起始日期'));
      } else {
        callback();
      }
    },
    trigger: 'change',
  };
};

export const getRemarkRule = (status: number) => {
  return {
    required: status === 2, // 撤回状态时必填
    message: '请输入备注',
    trigger: 'blur',
  };
};

// 基本信息表单配置
export const basicInfoSchema: FormSchema[] = [
  {
    field: 'supervisionCode',
    label: '序号',
    component: 'Input',
    componentProps: {
      placeholder: '序号为只读项',
      disabled: true,
    },
    helpMessage: '数据传到国资监管平台后将返回序号',
    colProps: { span: 8 },
  },
  {
    field: 'assetType',
    label: '资产类型',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择资产类型',
      options: [
        { label: '土地', value: 0 },
        { label: '房屋', value: 1 },
        { label: '设备', value: 2 },
        { label: '广告位', value: 3 },
        { label: '其他', value: 4 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'assetProjectName',
    label: '资产项目（资产名称）',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择资产项目（资产名称）',
      showSearch: true,
      filterOption: (input, option) =>
        option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      options: [
        { label: '土地资产1', value: '土地资产1' },
        { label: '房屋资产1', value: '房屋资产1' },
        { label: '设备资产1', value: '设备资产1' },
        { label: '广告位资产1', value: '广告位资产1' },
        { label: '其他资产1', value: '其他资产1' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'assetCode',
    label: '资产编号',
    component: 'Input',
    componentProps: {
      placeholder: '选择资产项目后自动带出',
      disabled: true,
    },
    colProps: { span: 8 },
  },
  {
    field: 'occupiedAssetName',
    label: '被占用资产名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入被占用资产名称',
    },
    colProps: { span: 8 },
  },
  {
    field: 'manageCompanyName',
    label: '管理单位',
    component: 'Input',
    componentProps: {
      placeholder: '选择资产项目后自动带出',
      disabled: true,
    },
    helpMessage: '将使用管理单位作为数据权限判断依据',
    colProps: { span: 8 },
  },
  {
    field: 'reported',
    label: '是否报送国资委',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '报送', value: 0 },
        { label: '不报送', value: 1 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'handlerUserName',
    label: '经办人',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入经办人',
    },
    colProps: { span: 8 },
  },
  {
    field: 'inputUserName',
    label: '录入人',
    component: 'Input',
    componentProps: {
      placeholder: '录入人',
      disabled: true,
    },
    colProps: { span: 8 },
  },
  {
    field: 'inputTime',
    label: '录入时间',
    component: 'Input',
    componentProps: {
      placeholder: '录入时间',
      disabled: true,
    },
    colProps: { span: 8 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    required: true,
    defaultValue: 0,
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '草稿', value: 0 },
        { label: '备案', value: 1 },
        { label: '撤回', value: 2 },
        { label: '作废', value: 4 },
      ],
    },
    helpMessage: '备案数据支持撤回、草稿数据和撤回数据支持作废',
    colProps: { span: 8 },
  },
];

// 借出信息表单配置
export const lendInfoSchema: FormSchema[] = [
  {
    field: 'loanStartDate',
    label: '借出起始日期',
    component: 'DatePicker',
    required: true,
    componentProps: {
      placeholder: '请选择借出起始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      disabledDate: (current) => current && current > new Date(),
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanEndDate',
    label: '借出结束日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择借出结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanDays',
    label: '借出天数',
    component: 'InputNumber',
    componentProps: {
      placeholder: '自动计算',
      disabled: true,
      style: { width: '100%' },
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanArea',
    label: '借出面积（㎡）',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入借出面积',
      precision: 2,
      min: 0,
      style: { width: '100%' },
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanOriginalValue',
    label: '借出资产原值（万元）',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入借出资产原值',
      precision: 2,
      min: 0,
      style: { width: '100%' },
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanBookValue',
    label: '借出资产账面价值（万元）',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入借出资产账面价值',
      precision: 2,
      min: 0,
      style: { width: '100%' },
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanBookValueDate',
    label: '账面价值时点',
    component: 'DatePicker',
    required: true,
    componentProps: {
      placeholder: '请选择账面价值时点',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanUserName',
    label: '借用人',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入借用人',
    },
    colProps: { span: 8 },
  },
  {
    field: 'changUse',
    label: '是否改变用途',
    component: 'Select',
    required: true,
    defaultValue: 1,
    componentProps: {
      placeholder: '请选择是否改变用途',
      options: [
        { label: '是', value: 0 },
        { label: '否', value: 1 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'loanReason',
    label: '借用原因',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      placeholder: '请输入借用原因',
      rows: 4,
    },
    colProps: { span: 24 },
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 4,
    },
    colProps: { span: 24 },
  },
];