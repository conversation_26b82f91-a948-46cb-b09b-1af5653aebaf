<template>
  <div class="transfer-form">
    <div class="p-4">
      <a-form :model="formData" :rules="rules" ref="formRef" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" layout="horizontal">
        <!-- 资产包信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:box-outlined" class="title-icon" />
              资产包信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="转让资产包编号" name="transferPackageCode">
                  <a-input v-model:value="formData.transferPackageCode" placeholder="保存后系统自动生成" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让资产包名称" name="transferPackageName">
                  <a-input v-model:value="formData.transferPackageName" placeholder="请输入转让资产包名称" />
                  <div class="help-text">系统内要求资产名称唯一</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让方式" name="transferMethod">
                  <JDictSelectTag
                    v-model:value="formData.transferMethod"
                    @change="handleTransferTypeChange"
                    :showChooseOption="false"
                    dictCode="transfer_method"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="主要资产类型" name="mainAssetType">
                  <JDictSelectTag v-model:value="formData.mainAssetType" :showChooseOption="false" dictCode="assets_type" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产所在地区" name="assetRegion">
                  <JAreaLinkage
                    v-model:value="formData.assetRegion"
                    placeholder="请选择省份/城市"
                    :showArea="false"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产来源" name="assetSource">
                  <JDictSelectTag v-model:value="formData.assetSource" :showChooseOption="false" dictCode="asset_source" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="管理单位" name="manageCompany">
                  <ApiSelect
                    v-model:value="formData.manageCompany"
                    placeholder="请选择管理单位"
                    :api="getUserCompany"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否报送国资委" name="reported">
                  <JDictSelectTag v-model:value="formData.reported" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经办人" name="handlerUserName">
                  <a-input v-model:value="formData.handlerUserName" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="录入人" name="inputUserName">
                  <a-input v-model:value="formData.inputUserName" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="录入时间" name="inputTime">
                  <a-input v-model:value="formData.inputTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态" name="status">
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option value="0" :disabled="initStatus === '1' || initStatus === '2' || initStatus === '4'">草稿</a-select-option>
                    <a-select-option value="1" :disabled="initStatus === '2' || initStatus === '4'">备案</a-select-option>
                    <a-select-option value="2" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '4'">撤回</a-select-option>
                    <a-select-option value="4" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '1'">作废</a-select-option>
                  </a-select>
                  <div class="help-text">备案数据支持撤回、草稿数据和撤回数据支持作废</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="备注" name="remark">
                  <a-input v-model:value="formData.remark" placeholder="请输入备注" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 挂牌信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:tag-outlined" class="title-icon" />
              挂牌信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="挂牌机构"
                  name="listingAgency"
                  :rules="formData.transferMethod === '1' ? [{ required: true, message: '请输入挂牌机构' }] : []"
                >
                  <a-input v-model:value="formData.listingAgency" placeholder="请输入挂牌机构" :disabled="formData.transferMethod !== '1'" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="挂牌机构-所在地区"
                  name="listingAgencyRegion"
                  :rules="formData.transferMethod === '1' ? [{ required: true, message: '请选择挂牌机构所在地区' }] : []"
                >
                  <JAreaLinkage
                    v-model:value="formData.listingAgencyRegion"
                    placeholder="请选择省份/城市"
                    :disabled="formData.transferMethod !== '1'"
                    :showArea="false"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 转让方信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:building-outlined" class="title-icon" />
              转让方信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="转让方名称" name="transferCompanyName">
                  <ApiSelect
                    v-model:value="formData.transferCompanyName"
                    placeholder="请选择转让方名称"
                    :api="getCompanyHandle"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让方类型" name="transferCompanyType">
                  <JDictSelectTag v-model:value="formData.transferCompanyType" :showChooseOption="false" dictCode="busines_type" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否联合转让" name="jointTransfer">
                  <JDictSelectTag v-model:value="formData.jointTransfer" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="转让方所在地区" name="transferCompanyRegion">
                  <JAreaLinkage
                    v-model:value="formData.transferCompanyRegion"
                    placeholder="请选择转让方所在地区"
                    :showArea="false"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="注册地地址" name="transferCompanyAddress">
                  <a-input v-model:value="formData.transferCompanyAddress" placeholder="请输入注册地地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="注册资本（万元）" name="transferCompanyCapital">
                  <a-input-number
                    v-model:value="formData.transferCompanyCapital"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                    placeholder="请输入注册资本"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="企业类型" name="transferCompanyBizType">
                  <JDictSelectTag v-model:value="formData.transferCompanyBizType" :showChooseOption="false" dictCode="company_type" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经济类型" name="transferCompanyEconomicType">
                  <JDictSelectTag v-model:value="formData.transferCompanyEconomicType" :showChooseOption="false" dictCode="economic_type" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="法定代表人" name="transferCompanyLegalRep">
                  <a-input v-model:value="formData.transferCompanyLegalRep" placeholder="请输入法定代表人" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="经营规模" name="transferCompanyBizScale">
                  <JDictSelectTag v-model:value="formData.transferCompanyBizScale" :showChooseOption="false" dictCode="business_scale" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="统一社会信用代码或组织机构代码" :label-col="{ span: 11 }" name="transferCompanyCreditCode">
                  <a-input v-model:value="formData.transferCompanyCreditCode" placeholder="请输入统一社会信用代码或组织机构代码" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="内部决策情况" name="transferCompanyDecision">
                  <JDictSelectTag v-model:value="formData.transferCompanyDecision" :showChooseOption="false" dictCode="inside_decision" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="附件" name="transferCompanyAttachments" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                  <JUpload
                    v-model:value="formData.transferCompanyAttachments"
                    :maxSize="50"
                    :maxNumber="10"
                    :multiple="true"
                    :download="true"
                    :removeConfirm="true"
                    :returnUrl="false"
                    :accept="['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.png', '.jpeg', '.jpg']"
                    helpMessage="文件格式为.doc、.docx、.xls、.xlsx、.pdf、.png、.jpeg、.jpg，单个文件最大不超过50M，可以上传多个附件。"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产评估核准或备案情况 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:clipboard-check-outlined" class="title-icon" />
              资产评估核准或备案情况
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="评估机构" name="evaluationAgency">
                  <a-input v-model:value="formData.evaluationAgency" placeholder="请输入评估机构" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="评估核准（备案）机构" name="evaluationApproveAgency">
                  <a-input v-model:value="formData.evaluationApproveAgency" placeholder="请输入评估核准（备案）机构" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="核准（备案）日期" name="evaluationApproveDate">
                  <a-date-picker
                    v-model:value="formData.evaluationApproveDate"
                    placeholder="请选择核准（备案）日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="评估基准日" name="evaluationBaseDate">
                  <a-input v-model:value="formData.evaluationBaseDate" placeholder="请输入评估基准日" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="评估报告文号" name="evaluationReportNo">
                  <a-input v-model:value="formData.evaluationReportNo" placeholder="请输入评估报告文号" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="评估基准日审计机构" name="evaluationAuditAgency">
                  <a-input v-model:value="formData.evaluationAuditAgency" placeholder="请输入评估基准日审计机构" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="律师事务所" name="evaluationLawFirm">
                  <a-input v-model:value="formData.evaluationLawFirm" placeholder="请输入律师事务所" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="标的评估值（万元）" name="evaluationTargetValue">
                  <a-input v-model:value="formData.evaluationTargetValue" placeholder="请输入标的评估值" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让标的评估总值（万元）" name="evaluationTotalValue">
                  <a-input v-model:value="formData.evaluationTotalValue" placeholder="请输入转让标的评估总值" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="账面原值（万元）" name="evaluationOriginalValue">
                  <a-input v-model:value="formData.evaluationOriginalValue" placeholder="请输入账面原值" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="账面净值（万元）" name="evaluationNetValue">
                  <a-input v-model:value="formData.evaluationNetValue" placeholder="请输入账面净值" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 披露信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              披露信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="挂牌（公示）开始时间"
                  name="disclosureStartTime"
                  :rules="['1', '2'].includes(formData.transferMethod) ? [{ required: true, message: '请选择挂牌（公示）开始时间' }] : []"
                >
                  <a-date-picker
                    v-model:value="formData.disclosureStartTime"
                    placeholder="请选择挂牌（公示）开始时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="挂牌（公示）截止时间"
                  name="disclosureEndTime"
                  :rules="['1', '2'].includes(formData.transferMethod) ? [{ required: true, message: '请选择挂牌（公示）截止时间' }] : []"
                >
                  <a-date-picker
                    v-model:value="formData.disclosureEndTime"
                    placeholder="请选择挂牌（公示）截止时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="内容描述"
                  name="disclosureContent"
                  :label-col="{ span: 3 }"
                  :wrapper-col="{ span: 20 }"
                  :rules="[{ required: true, message: '请输入内容描述' }]"
                >
                  <a-textarea v-model:value="formData.disclosureContent" :rows="4" placeholder="请输入内容描述" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="附件" name="disclosureAttachments" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                  <JUpload
                    v-model:value="formData.disclosureAttachments"
                    :maxSize="50"
                    :maxNumber="10"
                    :multiple="true"
                    :download="true"
                    :removeConfirm="true"
                    :returnUrl="false"
                    :accept="['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.png', '.jpeg', '.jpg']"
                    helpMessage="文件格式为.doc、.docx、.xls、.xlsx、.pdf、.png、.jpeg、.jpg，单个文件最大不超过50M，可以上传多个附件。"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 关联资产 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:link-outlined" class="title-icon" />
              关联资产
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addAssociatedAsset">
                <Icon icon="ant-design:plus-outlined" />
                添加关联资产
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <div v-if="formData.relatedAssetsList.length > 0">
              <a-table
                :dataSource="formData.relatedAssetsList"
                :columns="associatedAssetsColumns"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: 1200 }"
              >
                <template #bodyCell="{ column, record, index }">
                  <!-- 资产类型列 -->
                  <template v-if="column.dataIndex === 'assetType'">
                    <a-form-item
                      :name="['relatedAssetsList', index, 'assetType']"
                      :rules="[{ required: true, message: '请选择资产类型' }]"
                      class="mb-0"
                    >
                      <JDictSelectTag
                        v-model:value="record.assetType"
                        :showChooseOption="false"
                        dictCode="assets_type"
                        :getPopupContainer="getPopupContainer"
                        @change="() => handleAssetTypeChange(record, index)"
                      />
                    </a-form-item>
                  </template>

                  <!-- 资产名称（资产编号）列 -->
                  <template v-else-if="column.dataIndex === 'assetName'">
                    <a-form-item
                      :name="['relatedAssetsList', index, 'assetId']"
                      :rules="[{ required: true, message: '请选择资产名称（资产编号）' }]"
                      class="mb-0"
                    >
                      <AssetsSelect
                        v-model:value="record.assetId"
                        :type="record.assetType"
                        :initData="record.initData"
                        @change="(value, option) => handleAssetsCodeChange(record, value, option)"
                      />
                    </a-form-item>
                  </template>

                  <!-- 标的名称列 -->
                  <template v-else-if="column.dataIndex === 'targetName'">
                    <a-form-item
                      :name="['relatedAssetsList', index, 'targetName']"
                      :rules="[{ required: true, message: '请输入标的名称' }]"
                      class="mb-0"
                    >
                      <a-input v-model:value="record.targetName" placeholder="请输入标的名称" />
                    </a-form-item>
                  </template>

                  <!-- 挂牌价格列 -->
                  <template v-else-if="column.dataIndex === 'listingPrice'">
                    <a-form-item
                      :name="['relatedAssetsList', index, 'listingPrice']"
                      :rules="formData.transferMethod === '1' ? [{ required: true, message: '请输入挂牌价格' }] : []"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.listingPrice"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        style="width: 100%"
                        placeholder="请输入挂牌价格"
                      />
                    </a-form-item>
                  </template>

                  <!-- 评估价格列 -->
                  <template v-else-if="column.dataIndex === 'evaluationPrice'">
                    <a-form-item :name="['relatedAssetsList', index, 'evaluationPrice']" class="mb-0">
                      <a-input-number
                        v-model:value="record.evaluationPrice"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        style="width: 100%"
                        placeholder="请输入评估价格"
                      />
                    </a-form-item>
                  </template>

                  <!-- 转让面积列 -->
                  <template v-else-if="column.dataIndex === 'transferArea'">
                    <a-form-item
                      :name="['relatedAssetsList', index, 'transferArea']"
                      :rules="['0', '1'].includes(record.assetType) ? [{ required: true, message: '请输入转让面积' }] : []"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.transferArea"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        style="width: 100%"
                        placeholder="请输入转让面积"
                      />
                    </a-form-item>
                  </template>

                  <!-- 账面净值列 -->
                  <template v-else-if="column.dataIndex === 'bookNetValue'">
                    <a-form-item :name="['relatedAssetsList', index, 'bookNetValue']" class="mb-0">
                      <a-input-number
                        v-model:value="record.bookNetValue"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        style="width: 100%"
                        placeholder="请输入账面净值"
                      />
                    </a-form-item>
                  </template>

                  <!-- 操作列 -->
                  <template v-else-if="column.dataIndex === 'action'">
                    <a-button type="primary" danger size="small" shape="circle" @click="removeAssociatedAsset(index)">
                      <Icon icon="ant-design:delete-outlined" />
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
            <div v-if="formData.relatedAssetsList.length === 0" class="empty-hint" style="padding: 20px; text-align: center; color: #999;">
              暂无关联资产，请点击上方"添加关联资产"按钮添加。
            </div>
          </div>
        </div>

        <!-- 成交信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:handshake-outlined" class="title-icon" />
              成交信息
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addTransaction">
                <Icon icon="ant-design:plus-outlined" />
                添加成交信息
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <div v-if="formData.relatedDealInfoList.length > 0">
              <a-table
                :dataSource="formData.relatedDealInfoList"
                :columns="transactionsColumns"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: 1000 }"
              >
                <template #bodyCell="{ column, record, index }">
                  <!-- 标的名称列 -->
                  <template v-if="column.dataIndex === 'targetName'">
                    <a-form-item
                      :name="['relatedDealInfoList', index, 'targetName']"
                      :rules="[{ required: true, message: '请选择标的名称' }]"
                      class="mb-0"
                    >
                      <a-select v-model:value="record.targetName" placeholder="请选择标的名称" filterable>
                        <a-select-option v-for="asset in formData.relatedAssetsList" :key="asset.targetName" :value="asset.targetName">
                          {{ asset.targetName }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </template>

                  <!-- 是否成交列 -->
                  <template v-else-if="column.dataIndex === 'isDeal'">
                    <a-form-item
                      :name="['relatedDealInfoList', index, 'isDeal']"
                      :rules="[{ required: true, message: '请选择是否成交' }]"
                      class="mb-0"
                    >
                      <a-radio-group v-model:value="record.isDeal" @change="() => handleDealStatusChange(record, index)" style="white-space: nowrap">
                        <a-radio value="0" style="margin-right: 8px;">否</a-radio>
                        <a-radio value="1">是</a-radio>
                      </a-radio-group>
                    </a-form-item>
                  </template>

                  <!-- 受让方列 -->
                  <template v-else-if="column.dataIndex === 'transferee'">
                    <a-form-item
                      :name="['relatedDealInfoList', index, 'transferee']"
                      :rules="record.isDeal === '1' ? [{ required: true, message: '请输入受让方' }] : []"
                      class="mb-0"
                    >
                      <a-input v-model:value="record.transferee" placeholder="请输入受让方" :disabled="record.isDeal !== '1'" />
                    </a-form-item>
                  </template>

                  <!-- 成交价格列 -->
                  <template v-else-if="column.dataIndex === 'dealPrice'">
                    <a-form-item
                      :name="['relatedDealInfoList', index, 'dealPrice']"
                      :rules="record.isDeal === '1' ? [{ required: true, message: '请输入成交价格' }] : []"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.dealPrice"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        style="width: 100%"
                        placeholder="请输入成交价格"
                        :disabled="record.isDeal !== '1'"
                      />
                    </a-form-item>
                  </template>

                  <!-- 成交面积列 -->
                  <template v-else-if="column.dataIndex === 'dealArea'">
                    <a-form-item
                      :name="['relatedDealInfoList', index, 'dealArea']"
                      :rules="record.isDeal === '1' ? [{ required: true, message: '请输入成交面积' }] : []"
                      class="mb-0"
                    >
                      <a-input-number v-model:value="record.dealArea" placeholder="请输入成交面积" :disabled="record.isDeal !== '1'" />
                    </a-form-item>
                  </template>

                  <!-- 成交日期列 -->
                  <template v-else-if="column.dataIndex === 'dealDate'">
                    <a-form-item
                      :name="['relatedDealInfoList', index, 'dealDate']"
                      :rules="record.isDeal === '1' ? [{ required: true, message: '请选择成交日期' }] : []"
                      class="mb-0"
                    >
                      <a-date-picker
                        v-model:value="record.dealDate"
                        placeholder="请选择成交日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                        :disabled="record.isDeal !== '1'"
                      />
                    </a-form-item>
                  </template>

                  <!-- 操作列 -->
                  <template v-else-if="column.dataIndex === 'action'">
                    <a-button type="primary" danger size="small" shape="circle" @click="removeTransaction(index)">
                      <Icon icon="ant-design:delete-outlined" />
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
            <div v-if="formData.relatedDealInfoList.length === 0" class="empty-hint" style="padding: 20px; text-align: center; color: #999;">
              暂无成交信息，请点击上方"添加成交信息"按钮添加。
            </div>
          </div>
        </div>

        <!-- 表单提交 -->
        <div class="form-footer">
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" :loading="loading" @click="submitForm" style="margin-left: 12px">{{ submitButtonText }}</a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { saveOrUpdate, getDetail } from './transfer.api';
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import { JDictSelectTag, JAreaLinkage, ApiSelect } from '/@/components/Form';
  import { getUserCompany, getCompanyHandle } from '/@/api/common/api';
  import AssetsSelect from '/@/components/biz/select/assetsSelect.vue';
  import { useUserStore } from '/@/store/modules/user';
  import dayjs from 'dayjs';

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();

  // 表单引用
  const formRef = ref();

  // 页面状态
  const isEditMode = ref(false);
  const submitButtonText = ref('提交');
  const loading = ref(false);
  const initStatus = ref('');

  // 表格列定义
  const associatedAssetsColumns = [
    {
      title: '资产类型',
      dataIndex: 'assetType',
      key: 'assetType',
      width: 120,
      align: 'center',
    },
    {
      title: '资产名称（资产编号）',
      dataIndex: 'assetName',
      key: 'assetName',
      width: 220,
      align: 'center',
    },
    {
      title: '标的名称',
      dataIndex: 'targetName',
      key: 'targetName',
      width: 150,
      align: 'center',
    },
    {
      title: '挂牌价格（万元）',
      dataIndex: 'listingPrice',
      key: 'listingPrice',
      width: 150,
      align: 'center',
    },
    {
      title: '评估价格（万元）',
      dataIndex: 'evaluationPrice',
      key: 'evaluationPrice',
      width: 150,
      align: 'center',
    },
    {
      title: '转让面积（㎡）',
      dataIndex: 'transferArea',
      key: 'transferArea',
      width: 150,
      align: 'center',
    },
    {
      title: '账面净值（万元）',
      dataIndex: 'bookNetValue',
      key: 'bookNetValue',
      width: 150,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      align: 'center',
      fixed: 'right',
    },
  ];

  const transactionsColumns = [
    {
      title: '标的名称',
      dataIndex: 'targetName',
      key: 'targetName',
      width: 180,
      align: 'center',
    },
    {
      title: '是否成交',
      dataIndex: 'isDeal',
      key: 'isDeal',
      width: 120,
      align: 'center',
    },
    {
      title: '受让方',
      dataIndex: 'transferee',
      key: 'transferee',
      width: 180,
      align: 'center',
    },
    {
      title: '成交价格（万元）',
      dataIndex: 'dealPrice',
      key: 'dealPrice',
      width: 150,
      align: 'center',
    },
    {
      title: '成交面积',
      dataIndex: 'dealArea',
      key: 'dealArea',
      width: 150,
      align: 'center',
    },
    {
      title: '成交日期',
      dataIndex: 'dealDate',
      key: 'dealDate',
      width: 150,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      align: 'center',
      fixed: 'right',
    },
  ];

  // 表单数据
  const formData = reactive({
    // 资产包信息
    transferPackageCode: '',
    transferPackageName: '',
    transferMethod: '',
    mainAssetType: '',
    assetRegion: [],
    assetSource: '',
    manageCompany: '',
    reported: '',
    handlerUserName: '',
    inputUserName: userStore.getUserInfo.realname,
    inputTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    status: '',
    remark: '',

    // 挂牌信息
    listingAgency: '',
    listingAgencyRegion: [],

    // 转让方信息
    transferCompanyName: '',
    transferCompanyType: '',
    jointTransfer: '',
    transferCompanyRegion: [],
    transferCompanyAddress: '',
    transferCompanyCapital: '',
    transferCompanyBizType: '',
    transferCompanyEconomicType: '',
    transferCompanyLegalRep: '',
    transferCompanyBizScale: '',
    transferCompanyCreditCode: '',
    transferCompanyDecision: '',
    transferCompanyAttachments: [],

    // 资产评估核准或备案情况
    evaluationAgency: '',
    evaluationApproveAgency: '',
    evaluationApproveDate: '',
    evaluationBaseDate: '',
    evaluationReportNo: '',
    evaluationAuditAgency: '',
    evaluationLawFirm: '',
    evaluationTargetValue: '',
    evaluationTotalValue: '',
    evaluationOriginalValue: '',
    evaluationNetValue: '',

    // 披露信息
    disclosureStartTime: '',
    disclosureEndTime: '',
    disclosureContent: '',
    disclosureAttachments: [],

    // 关联资产列表
    relatedAssetsList: [] as Array<{
      assetType: string;
      assetId: string;
      assetCode: string;
      targetName: string;
      listingPrice: number | null;
      evaluationPrice: number | null;
      transferArea: number | null;
      bookNetValue: number | null;
      uid?: string;
      initData?: any;
    }>,

    // 成交信息列表
    relatedDealInfoList: [] as Array<{
      targetName: string;
      isDeal: string;
      transferee: string;
      dealPrice: number | null;
      dealArea: string;
      dealDate: null
      uid?: string;
    }>,
  });

  // 表单验证规则
  const rules = {
    // 资产包信息
    transferPackageName: [{ required: true, message: '请输入转让资产包名称', trigger: 'blur' }],
    transferMethod: [{ required: true, message: '请选择转让方式', trigger: 'change' }],
    mainAssetType: [{ required: true, message: '请选择主要资产类型', trigger: 'change' }],
    assetRegion: [{ required: true, message: '请选择资产所在地区', trigger: 'change' }],
    assetSource: [{ required: true, message: '请选择资产来源', trigger: 'change' }],
    manageCompany: [{ required: true, message: '请选择管理单位', trigger: 'change' }],
    reported: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    handlerUserName: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],

    // 转让方信息
    transferCompanyName: [{ required: true, message: '请选择转让方名称', trigger: 'change' }],
    transferCompanyCapital: [{ pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: '请输入有效的金额（保留两位小数）', trigger: 'blur' }],
    transferCompanyCreditCode: [
      // {
      //   pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$|^[0-9A-Z]{9}$/,
      //   message: '请输入正确的统一社会信用代码或组织机构代码格式',
      //   trigger: 'blur',
      // },
    ],

    // 资产评估核准或备案情况
    evaluationTargetValue: [{ pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: '请输入有效的金额（保留两位小数）', trigger: 'blur' }],
    evaluationTotalValue: [{ pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: '请输入有效的金额（保留两位小数）', trigger: 'blur' }],

    // 披露信息
    disclosureContent: [{ required: true, message: '请输入内容描述', trigger: 'blur' }],
  };

  function getPopupContainer() {
    return document.body;
  }

  // 生成唯一ID
  function generateUid() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 转让方式变化处理
  function handleTransferTypeChange(value: number) {
    if (value !== 1) {
      formData.listingAgency = '';
      formData.listingAgencyRegion = [];
    }
  }

  // 添加关联资产
  function addAssociatedAsset() {
    formData.relatedAssetsList.push({
      assetType: '',
      assetId: '',
      assetCode: '',
      targetName: '',
      listingPrice: null,
      evaluationPrice: null,
      transferArea: null,
      bookNetValue: null,
      uid: generateUid(),
      initData: null,
    });
  }

  // 删除关联资产
  function removeAssociatedAsset(index: number) {
    formData.relatedAssetsList.splice(index, 1);
  }

  // 资产类型变化处理
  function handleAssetTypeChange(record: any, _index: number) {
    // 清空资产编号选择
    record.assetId = '';
    record.assetCode = '';
    record.targetName = '';
    record.initData = null;
  }

  // 资产编号变化处理
  function handleAssetsCodeChange(record: any, value: string, options: any) {
    // 根据选择的资产编号自动生成标的名称
    record.targetName = options.name || options.label;
    record.assetCode = options.code || value;
  }

  // 添加成交信息
  function addTransaction() {
    formData.relatedDealInfoList.push({
      targetName: '',
      isDeal: '0',
      transferee: '',
      dealPrice: null,
      dealArea: '',
      dealDate: null,
      uid: generateUid(),
    });
  }

  // 删除成交信息
  function removeTransaction(index: number) {
    formData.relatedDealInfoList.splice(index, 1);
  }

  // 成交状态变化处理
  function handleDealStatusChange(record: any, _index: number) {
    if (record.isDeal === '0') {
      // 如果选择"否"，清空相关字段
      record.transferee = '';
      record.dealPrice = null;
      record.dealArea = '';
      record.dealDate = null;
    }
  }

  // 提交表单
  async function submitForm() {
    try {
      await formRef.value.validate();
      loading.value = true;

      const submitData = {
        ...formData,
        assetRegion: formData.assetRegion.join(','),
        listingAgencyRegion: formData.listingAgencyRegion.join(','),
        transferCompanyRegion: formData.transferCompanyRegion.join(','),
      };
      if (isEditMode.value) {
        submitData.id = route.params.id;
      }
      await saveOrUpdate(submitData);

      message.success(isEditMode.value ? '更新成功' : '新增成功');
      router.push('/transfer/info');
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 重置表单
  function resetForm() {
    formRef.value.resetFields();
  }

  // 初始化
  onMounted(async () => {
    const { id } = route.params;
    if (id) {
      isEditMode.value = true;
      submitButtonText.value = '更新';

      try {
        const data = await getDetail(id as string);
        if (data) {
          Object.assign(formData, {
            ...data,
            transferMethod: `${data.transferMethod}`,
            mainAssetType: `${data.mainAssetType}`,
            assetRegion: data.assetRegion ? data.assetRegion.split(',') : [],
            assetSource: `${data.assetSource}`,
            manageCompany: +data.manageCompany,
            reported: `${data.reported}`,
            status: `${data.status}`,
            listingAgencyRegion: data.listingAgencyRegion ? data.listingAgencyRegion.split(',') : [],
            transferCompanyName: +data.transferCompanyName,
            transferCompanyType: `${data.transferCompanyType || ''}`,
            jointTransfer: `${data.jointTransfer || ''}`,
            transferCompanyRegion: data.transferCompanyRegion ? data.transferCompanyRegion.split(',') : [],
            transferCompanyEconomicType: `${data.transferCompanyEconomicType || ''}`,
            transferCompanyDecision: `${data.transferCompanyDecision || ''}`,

            transferCompanyAttachments: data.transferCompanyAttachments || [],
            disclosureAttachments: data.disclosureAttachments || [],

            relatedAssetsList: (data.relatedAssetsList || []).map(item => {
              return {
                ...item,
                assetType: `${item.assetType}`,
                initData: {
                  name: item.assetName,
                  code: item.assetCode,
                  value: item.assetId,
                  label: `${item.assetName}(${item.assetCode})`,
                },
              };
            }),
            relatedDealInfoList: (data.relatedDealInfoList || []).map(item => {
              return {
                ...item,
                isDeal: `${item.isDeal}`,
              };
            }),
          });
          initStatus.value = `${data.status}` || '';
        }
      } catch (error) {
        message.error('加载详情失败');
      }
    } else {
      // 新增模式，设置默认状态为草稿
      formData.status = '0';
      initStatus.value = '0';
    }
  });
</script>

<style lang="less" scoped>
  .transfer-form {
    .simple-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
      padding: 16px 24px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .form-card {
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .form-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        .form-card-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          color: #333;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 24px;
      }
    }

    .form-footer {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 4px;
    }

    .empty-hint {
      text-align: center;
      color: #999;
      font-size: 14px;
      padding: 40px 20px;
      background: #fafafa;
      border-radius: 4px;
    }
  }
</style>