<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleExport" :disabled="selectedRowKeys.length === 0"> 导出 </a-button>
        <a-button type="warning" preIcon="ant-design:download-outlined" @click="handleExportAll">全部导出</a-button>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="rent-detail" setup>
  import { ref, computed } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './rentDetail.data';
  import { getRentDetailList, exportExcel, querySum } from './rentDetail.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  import dayjs from 'dayjs';
  import { useMethods } from '/@/hooks/system/useMethods';
  import quarterOfYear from 'dayjs/plugin/quarterOfYear';
  import 'dayjs/locale/zh-cn';

  // 确保 dayjs 使用中文本地化和季度插件
  dayjs.extend(quarterOfYear);
  dayjs.locale('zh-cn');

  const { createMessage } = useMessage();
  const { handleExportXls } = useMethods();

  const exportLoading = ref(false);

  const sumMap = ref({});

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'rent-detail-list',
    tableProps: {
      title: '租金明细列表',
      api: getRentDetailList,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: false,
      // 显示表格设置
      showTableSetting: true,
      showActionColumn: false,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'rent_detail_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 120,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
      },
      beforeFetch: (params) => {
        // 处理季度范围参数
        console.log('searchFormSchema:', params);
        if (params.quarterRange) {
          const [start, end] = params.quarterRange.split(',');
          const [startYear, startQuarter] = start.split('-');
          const [endYear, endQuarter] = end.split('-');
          params.yearMin = startYear;
          params.yearMax = endYear;
          params.seasonMin = startQuarter;
          params.seasonMax = endQuarter;
          delete params.quarterRange;
        }
        getSumHandle(params);
        return params;
      },
      // 显示合计行
      showSummary: true,
      summaryData: computed(() => {
        // 使用工具函数自动计算合计
        const totals = mapTableTotalSummary(
          [],
          ['targetArea', 'planTotalRent', 'receivableRent', 'receiptsRent', 'unpaidRent', 'defaultRent', 'reductionRent']
        );

        return [
          {
            ...totals,
            _index: '',
            code: '当前页合计',
            targetArea: sumMap.value?.targetAreaCurrentSum,
            planTotalRent: sumMap.value?.planTotalRentCurrentSum,
            receivableRent: sumMap.value?.receivableRentCurrentSum,
            receiptsRent: sumMap.value?.receiptsRentCurrentSum,
            unpaidRent: sumMap.value?.unpaidRentCurrentSum,
            defaultRent: sumMap.value?.defaultRentCurrentSum,
            reductionRent: sumMap.value?.reductionRentCurrentSum,
          },
          {
            ...totals,
            _index: '',
            code: '合计',
            targetArea: sumMap.value?.targetAreaSum,
            planTotalRent: sumMap.value?.planTotalRentSum,
            receivableRent: sumMap.value?.receivableRentSum,
            receiptsRent: sumMap.value?.receiptsRentSum,
            unpaidRent: sumMap.value?.unpaidRentSum,
            defaultRent: sumMap.value?.defaultRentSum,
            reductionRent: sumMap.value?.reductionRentSum,
          },
        ];
      }),
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  function getSumHandle(params) {
    querySum(params).then((res) => {
      // console.log(res, 'res');
      sumMap.value = res;
    });
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm().getFieldsValue();
    formData.ids = selectedRowKeys.value;
    exportLoading.value = true;
    await handleExportXls('租金明细列表', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    const formData = getForm().getFieldsValue();
    exportLoading.value = true;
    await handleExportXls('租金明细列表', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }
</script>

<style lang="less" scoped>
  // 可以添加自定义样式
</style>