<template>
  <a-select
    v-bind="getAttrs"
    placeholder="请选择公告"
    show-search
    :filter-option="false"
    mode="multiple"
    :not-found-content="loading ? '加载中...' : null"
    @search="handleRelatedPublicLeasingSearch"
    @focus="() => handleRelatedPublicLeasingSearch('')"
    @change="handleChange"
    :options="assetsOptions"
  />
</template>

<script lang="ts">
  import { defineComponent, ref, computed, watch } from 'vue';
  import { getNoticByName } from './assetsSelect.api';
  import { useMessage } from '/@/hooks/web/useMessage';

  export default defineComponent({
    name: 'LinkAssetsModal',
    props: {
      value: {
        type: String,
        default: '',
      },
      initData: {
        type: Object,
        default: () => null,
      },
    },
    emits: ['change', 'update:value'],
    setup(props, { attrs, emit }) {
      const loading = ref(false);
      const assetsOptions = ref([]);
      const init = ref(true);

      const getAttrs = computed(() => {
        return {
          value: props.value,
          ...attrs,
        };
      });

      // if (props.initData) {
      //   console.log('props.initData', props.initData);
      //   assetsOptions.value = [props.initData];
      // }

      function handleChange(value, option) {
        emit('change', value, option);
        emit('update:value', value, option);
      }

      function handleRelatedPublicLeasingSearch(value) {
        loading.value = true;
        console.log("-----------------------------")
        if (init.value && props.initData) {
          init.value = false;
          
          console.log(props.initData);
          console.log(props.value);
          assetsOptions.value = props.initData.map((item) => ({ ...item, value: item.id, label: item.name }));
          return;
        }
        getNoticByName({ name: value })
          .then((res) => {
            console.log(res);
            assetsOptions.value = res.map((item) => ({ ...item, value: item.id, label: item.name }));
          })
          .finally(() => {
            loading.value = false;
          });
      }

      handleRelatedPublicLeasingSearch('');

      return {
        getAttrs,
        assetsOptions,
        loading,
        handleRelatedPublicLeasingSearch,
        handleChange,
      };
    },
  });
</script>

<style lang="less" scoped>
</style> 