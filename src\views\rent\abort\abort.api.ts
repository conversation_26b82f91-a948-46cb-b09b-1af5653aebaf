import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/rent/abort/queryPage',

  save = '/rent/abort/add',
  edit = '/rent/abort/edit',
  delete = '/rent/abort/delete/',

  importExcel = '/rent/abort/excel/import',
  exportXls = '/rent/abort/excel/export',
  downloadTemplate = '/rent/abort/downloadImportTemplate',

  detail = '/rent/abort/info/',
  querySum = '/rent/abort/querySum',

  getRentInfoAssetPackageInfo = '/biz/rentInfo/getAssetPackageInfo/',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  const method = isUpdate ? 'put' : 'post';
  return defHttp[method]({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail + id, params: {} });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteAbort = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete + params.id, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 汇总
 * @param params
 */
export const querySum = (params) => defHttp.post({ url: Api.querySum, params });

export const getRentInfoAssetPackageInfo = (params) => {
  return defHttp.get({
    url: Api.getRentInfoAssetPackageInfo,
    params,
  });
};
