

## 借出信息-添加


**接口地址**:`/jeecg-boot/biz/loan/add`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "assetCode": "",
  "assetProjectName": "",
  "assetType": "",
  "changUse": 0,
  "createBy": "",
  "createTime": "",
  "handlerUserName": "",
  "id": 0,
  "inputTime": "",
  "inputUserName": "",
  "loanArea": 0,
  "loanBookValue": "",
  "loanBookValueDate": "",
  "loanDays": 0,
  "loanEndDate": "",
  "loanOriginalValue": "",
  "loanReason": "",
  "loanStartDate": "",
  "loanUserName": "",
  "manageCompanyCode": "",
  "manageCompanyName": "",
  "occupiedAssetName": "",
  "remark": "",
  "reported": 0,
  "status": 0,
  "supervisionCode": "",
  "updateBy": "",
  "updateTime": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|loanRecordsEntity|loanRecordsEntity|body|true|借出信息表单|借出信息表单|
|&emsp;&emsp;assetCode|基础信息-资产编号||false|string||
|&emsp;&emsp;assetProjectName|基础信息-资产项目名称||false|string||
|&emsp;&emsp;assetType|基础信息-资产类型（枚举）||false|string||
|&emsp;&emsp;changUse|借出信息-是否改变用途:0-是,1-否||false|integer(int32)||
|&emsp;&emsp;createBy|创建人||false|string||
|&emsp;&emsp;createTime|创建时间||false|string(date-time)||
|&emsp;&emsp;handlerUserName|基础信息-经办人||false|string||
|&emsp;&emsp;id|ID||false|integer(int32)||
|&emsp;&emsp;inputTime|基础信息-录入时间||false|string(date-time)||
|&emsp;&emsp;inputUserName|基础信息-录入人||false|string||
|&emsp;&emsp;loanArea|借出信息-借出面积||false|number||
|&emsp;&emsp;loanBookValue|借出信息-借出资产账面价值（万元）||false|string||
|&emsp;&emsp;loanBookValueDate|借出信息-账面价值时点||false|string(date-time)||
|&emsp;&emsp;loanDays|借出信息-借出天数||false|integer(int32)||
|&emsp;&emsp;loanEndDate|借出信息-借出结束日期||false|string(date-time)||
|&emsp;&emsp;loanOriginalValue|借出信息-借出资产源值（万元）||false|string||
|&emsp;&emsp;loanReason|借出信息-借用原因||false|string||
|&emsp;&emsp;loanStartDate|借出信息-借出起始日期||false|string(date-time)||
|&emsp;&emsp;loanUserName|借出信息-借用人||false|string||
|&emsp;&emsp;manageCompanyCode|基础信息-管理单位唯一编码||false|string||
|&emsp;&emsp;manageCompanyName|基础信息-管理单位名称||false|string||
|&emsp;&emsp;occupiedAssetName|基础信息-被占用资产名称||false|string||
|&emsp;&emsp;remark|借出信息-备注||false|string||
|&emsp;&emsp;reported|基础信息-是否报送国资委:0-报送,1-不报送||false|integer(int32)||
|&emsp;&emsp;status|基础信息-状态（枚举）||false|integer(int32)||
|&emsp;&emsp;supervisionCode|基础信息-序号:数据传到国资监管平台后将返回序号||false|string||
|&emsp;&emsp;updateBy|更新人||false|string||
|&emsp;&emsp;updateTime|更新时间||false|string(date-time)||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«string»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|string||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": "",
	"success": true,
	"timestamp": 0
}
```