import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/biz/rent/detail/queryPage',
  exportXls = '/biz/rent/detail/excel/export',
  querySum = '/biz/rent/detail/querySum',
}

export const exportExcel = Api.exportXls;

/**
 * 获取租金明细列表
 * @param params
 */
export const getRentDetailList = (params) => defHttp.post({ url: Api.list, params });

/**
 * 汇总
 * @param params
 */
export const querySum = (params) => defHttp.post({ url: Api.querySum, params }); 

