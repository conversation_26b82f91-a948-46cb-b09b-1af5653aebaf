import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '租赁资产包名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '租赁资产包编号',
    dataIndex: 'code',
    width: 150,
    fixed: 'left',
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnitName',
    width: 240,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no', true);
    },
  },
  {
    title: '是否成交',
    dataIndex: 'abortStatus',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no', true);
    },
  },
  {
    title: '出租方式',
    dataIndex: 'rentType',
    width: 180,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'rent_type');
    },
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '中止日期',
    dataIndex: 'abortDate',
    width: 120,
  },
  {
    title: '实收总租金(万元)',
    dataIndex: 'actTotalRent',
    width: 150,
    align: 'right',
  },
  {
    title: '中止原因',
    dataIndex: 'abortReason',
    width: 250,
    ellipsis: true,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '租赁资产包名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包名称',
    },
  },
  {
    label: '租赁资产包编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包编号',
    },
  },
  {
    label: '出租方式',
    field: 'rentType',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择出租方式',
      dictCode: 'rent_type',
    },
  },
  {
    label: '是否成交',
    field: 'abortStatus',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择是否成交',
      dictCode: 'yes_no',
    },
  },
  {
    label: '管理单位',
    field: 'manageUnit',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '是否报送国资委',
    field: 'reportOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '经办人',
    field: 'operator',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    label: '标的名称',
    field: 'targetName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的名称',
    },
  },
  {
    label: '中止日期范围',
    field: 'abortDate',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '实收总租金大于',
    field: 'actTotalRent',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入金额(万元)',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    label: '录入人',
    field: 'entryClerk',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    label: '录入时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '更新时间',
    field: 'updateTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
