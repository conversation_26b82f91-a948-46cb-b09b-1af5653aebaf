import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '租赁资产包编号',
    dataIndex: 'code',
    width: 180,
    fixed: 'left',
  },
  {
    title: '租赁资产包名称',
    dataIndex: 'name',
    width: 180,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '资产类型',
    dataIndex: 'type',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'assets_type');
    },
  },
  {
    title: '标的所占面积(㎡)',
    dataIndex: 'targetArea',
    width: 150,
    align: 'right',
  },
  {
    title: '成交日期',
    dataIndex: 'dealDate',
    width: 120,
  },
  {
    title: '承租开始时间',
    dataIndex: 'dealBeginDate',
    width: 140,
  },
  {
    title: '承租结束时间',
    dataIndex: 'dealEndDate',
    width: 140,
  },
  {
    title: '拟收总租金(万元)',
    dataIndex: 'planTotalRent',
    width: 150,
    align: 'right',
  },
  {
    title: '年份',
    dataIndex: 'year',
    width: 100,
  },
  {
    title: '季度',
    dataIndex: 'quarter',
    width: 100,
  },
  {
    title: '应收租金(万元)',
    dataIndex: 'receivableRent',
    width: 150,
    align: 'right',
  },
  {
    title: '实收租金(万元)',
    dataIndex: 'receiptsRent',
    width: 150,
    align: 'right',
  },
  {
    title: '未收租金(万元)',
    dataIndex: 'unpaidRent',
    width: 150,
    align: 'right',
  },
  {
    title: '违约租金(万元)',
    dataIndex: 'defaultRent',
    width: 150,
    align: 'right',
  },
  {
    title: '减免租金(万元)',
    dataIndex: 'reductionRent',
    width: 150,
    align: 'right',
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnit',
    width: 200,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '资产包名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包名称',
    },
  },
  {
    label: '资产包编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包编号',
    },
  },
  {
    label: '出租方式',
    field: 'rentType',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择出租方式',
      dictCode: 'rental_type',
    },
  },
  {
    label: '资产类型',
    field: 'assetType',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择资产类型',
      dictCode: 'assets_type',
    },
  },
  {
    label: '标的名称',
    field: 'targetName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的名称',
    },
  },
  {
    label: '管理单位',
    field: 'manageUnit',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '季度范围',
    field: 'quarterRange',
    component: 'RangePicker',
    componentProps: {
      picker: 'quarter',
      placeholder: ['开始季度', '结束季度'],
      format: 'YYYY-MM',
      valueFormat: 'YYYY-MM',
      style: { width: '100%' },
    },
  },
];
