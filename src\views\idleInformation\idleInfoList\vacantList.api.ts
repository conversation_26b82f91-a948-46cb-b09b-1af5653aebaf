import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/biz/assets/leave/queryPage',

  save = '/biz/assets/leave/add',
  edit = '/biz/assets/leave/edit',

  delete = '/biz/assets/leave/delete/',

  importExcel = '/biz/assets/leave/excel/import',
  exportXls = '/biz/assets/leave/excel/export',
  downloadTemplate = '/biz/assets/leave/downloadImportTemplate',

  detail = '/biz/assets/leave/info/',

  querySum = '/biz/assets/leave/querySum',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  const method = isUpdate ? 'put' : 'post';
  return defHttp[method]({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail + id, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteVacant = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete + params.id, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 获取合计
 * @param params
 */
export const querySum = (params) => defHttp.post({ url: Api.querySum, params });
