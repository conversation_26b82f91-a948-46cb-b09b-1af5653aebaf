<template>
  <div class="occupy-form">
    <div class="p-4">
      <a-form :model="formData" :rules="rules" ref="occupyFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="supervisionCode">
                  <template #label>
                    <span>序号</span>
                    <a-tooltip title="数据传到国资监管平台后将返回序号">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.supervisionCode" placeholder="序号为只读项" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetType" label="资产类型">
                  <JDictSelectTag
                    v-model:value="formData.assetType"
                    :showChooseOption="false"
                    dictCode="assets_type"
                    @change="handleAssetTypeChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetId" :label-col="{ span: 8 }" label="资产项目（资产名称）">
                  <AssetsSelect
                    v-model:value="formData.assetId"
                    :type="formData.assetType"
                    :disabled="!formData.assetType"
                    :initData="formData.initData"
                    @change="handleAssetChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetCode" label="资产编号">
                  <a-input v-model:value="formData.assetCode" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedAssetName" label="被占用资产名称">
                  <a-input v-model:value="formData.occupiedAssetName" placeholder="请输入被占用资产名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="manageCompanyName" label="管理单位">
                  <a-input v-model:value="formData.manageCompanyName" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="reported" label="是否报送国资委">
                  <JDictSelectTag v-model:value="formData.reported" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="handlerUserName" label="经办人">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="inputUserName" label="录入人">
                  <a-input v-model:value="formData.inputUserName" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="inputTime" label="录入时间">
                  <a-input v-model:value="formData.inputTime" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status">
                  <template #label>
                    <span>状态</span>
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option :value="0" :disabled="isDraftDisabled">草稿</a-select-option>
                    <a-select-option :value="1" :disabled="isFiledDisabled">备案</a-select-option>
                    <a-select-option :value="2" :disabled="isRevokedDisabled">撤回</a-select-option>
                    <a-select-option :value="4" :disabled="isVoidDisabled">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 占用信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:user-check-outlined" class="title-icon" />
              占用信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="occupiedStartDate" label="占用起始日期">
                  <a-date-picker
                    v-model:value="formData.occupiedStartDate"
                    placeholder="请选择占用起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="startDateDisabled"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedEndDate" label="占用结束日期">
                  <a-date-picker
                    v-model:value="formData.occupiedEndDate"
                    placeholder="请选择占用结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="endDateDisabled"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="占用天数">
                  <a-input v-model:value="formData.occupiedDays" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedArea" label="占用面积（㎡）">
                  <a-input-number
                    v-model:value="formData.occupiedArea"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入占用面积"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedUserName" label="占用人">
                  <a-input v-model:value="formData.occupiedUserName" placeholder="请输入占用人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedOriginalValue" :label-col="{ span: 8 }" label="占用资产原值（万元）">
                  <a-input-number
                    v-model:value="formData.occupiedOriginalValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入占用资产原值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedBookValue" :label-col="{ span: 10 }" label="占用资产账面价值（万元）">
                  <a-input-number
                    v-model:value="formData.occupiedBookValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入占用资产账面价值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedBookValueDate" label="账面价值时点">
                  <a-date-picker
                    v-model:value="formData.occupiedBookValueDate"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="changUse" label="是否改变用途">
                  <JDictSelectTag v-model:value="formData.changUse" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="illegal" label="是否违建">
                  <JDictSelectTag v-model:value="formData.illegal" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="occupiedReason" :label-col="{ span: 2 }" label="占用原因">
                  <a-textarea v-model:value="formData.occupiedReason" placeholder="请输入占用原因" :rows="4" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="remark" :label-col="{ span: 2 }" label="备注">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 盘活记录 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:sync-outlined" class="title-icon" />
              盘活记录
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addDealRecord">
                <Icon icon="ant-design:plus-outlined" />
                新增盘活记录
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <a-table
              :dataSource="occupancyRevitalizationRecords"
              :columns="dealColumns"
              :pagination="false"
              :scroll="{ x: 1200 }"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'revitalizeDate'">
                  <a-form-item
                    :name="['occupancyRevitalizationRecords', index, 'revitalizeDate']"
                    :rules="[{ required: true, message: '请选择日期', trigger: 'change' }]"
                  >
                    <a-date-picker
                      v-model:value="record.revitalizeDate"
                      placeholder="请选择日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'revitalized'">
                  <a-form-item
                    :name="['occupancyRevitalizationRecords', index, 'revitalized']"
                    :rules="[{ required: true, message: '请选择是否已盘活', trigger: 'change' }]"
                  >
                    <JDictSelectTag
                      style="width: 100%"
                      placeholder="请选择是否已盘活"
                      v-model:value="record.revitalized"
                      :getPopupContainer="getPopupContainer"
                      :showChooseOption="false"
                      dictCode="yes_no"
                    />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'revitalizeType'">
                  <a-form-item
                    :name="['occupancyRevitalizationRecords', index, 'revitalizeType']"
                    :rules="[{ required: true, message: '请选择盘活方式', trigger: 'change' }]"
                  >
                    <JDictSelectTag
                      placeholder="请选择盘活方式"
                      v-model:value="record.revitalizeType"
                      style="width: 100%"
                      :getPopupContainer="getPopupContainer"
                      :showChooseOption="false"
                      dictCode="vitalize_type"
                    />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'revitalizationMeasures'">
                  <a-textarea v-model:value="record.revitalizationMeasures" placeholder="请输入已采取的盘活管理措施" :rows="2" />
                </template>
                <template v-else-if="column.key === 'strategicSuggestions'">
                  <a-textarea v-model:value="record.strategicSuggestions" placeholder="请输入下一步建议" :rows="2" />
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-button type="link" danger size="small" @click="removeDealRecord(index)">
                    <Icon icon="ant-design:delete-outlined" />
                    删除
                  </a-button>
                </template>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="loading" style="margin-left: 12px">提交</a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="OccupyForm" setup>
  import { ref, onMounted, computed, watch, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { saveOrUpdate, getDetail } from './occupy.api';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import AssetsSelect from '/@/components/biz/select/assetsSelect.vue';
  import dayjs from 'dayjs';
  import { JDictSelectTag } from '/@/components/Form';
  import { useUserStore } from '/@/store/modules/user';

  // 定义盘活记录接口
  interface DealRecord {
    revitalizeDate: string; // 日期
    revitalized: string; // 是否已盘活
    revitalizeType: string; // 盘活方式
    revitalizationMeasures: string; // 已采取的盘活管理措施
    strategicSuggestions: string; // 下一步建议
  }

  // 定义表单数据接口
  interface FormData {
    id: string;
    // 基本信息
    supervisionCode?: string; // 序号
    assetType: string; // 资产类型
    assetId: string; // 资产项目（资产名称）
    assetCode: string; // 资产编号
    occupiedAssetName: string; // 被占用资产名称
    manageCompanyName: string; // 管理单位
    reported: string; // 是否报送国资委
    operator: string; // 经办人
    inputUserName: string; // 录入人
    inputTime: string; // 录入时间
    status: string; // 状态
    // 占用信息
    occupiedStartDate: string; // 占用起始日期
    occupiedEndDate: string; // 占用结束日期
    occupiedDays: string; // 占用天数
    occupiedArea: number; // 占用面积（㎡）
    occupiedUserName: string; // 占用人
    occupiedOriginalValue: number; // 占用资产原值（万元）
    occupiedBookValue: number; // 占用资产账面价值（万元）
    occupiedBookValueDate: string; // 账面价值时点
    changUse: string; // 是否改变用途
    illegal: string; // 是否违建
    occupiedReason: string; // 占用原因
    remark: string; // 备注
    // 盘活记录
    occupancyRevitalizationRecords: DealRecord[];
    initData?: any;
  }

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);
  const occupyFormRef = ref();

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');
  const originalStatus = ref<number | null>(null);

  // 表单数据
  const formData = ref<FormData>({
    // 基本信息
    id: '',
    supervisionCode: '',
    assetType: '',
    assetId: '',
    assetCode: '',
    occupiedAssetName: '',
    manageCompanyName: '',
    reported: '',
    operator: '',
    inputUserName: userStore.getUserInfo.realname,
    inputTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    status: '',
    // 占用信息
    occupiedStartDate: '',
    occupiedEndDate: '',
    occupiedDays: '',
    occupiedArea: 0,
    occupiedUserName: '',
    occupiedOriginalValue: 0,
    occupiedBookValue: 0,
    occupiedBookValueDate: '',
    changUse: '',
    illegal: '',
    occupiedReason: '',
    remark: '',
    // 盘活记录
    occupancyRevitalizationRecords: [],
  });

  // 盘活记录列表
  const occupancyRevitalizationRecords = ref<DealRecord[]>([]);

  // 表单验证规则
  const rules = {
    // 基本信息验证规则
    assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
    assetId: [{ required: true, message: '请选择资产项目（资产名称）', trigger: 'change' }],
    assetCode: [{ required: true, message: '请选择资产项目（资产名称）后自动带出资产编号', trigger: 'change' }],
    manageCompanyName: [{ required: true, message: '管理单位为必填项', trigger: 'change' }],
    reported: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    operator: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    inputUserName: [{ required: true, message: '录入人为必填项' }],
    inputTime: [{ required: true, message: '录入时间为必填项' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],

    // 占用信息验证规则
    occupiedStartDate: [{ required: true, message: '请选择占用起始日期', trigger: 'change' }],
    occupiedEndDate: [
      {
        validator: (rule: any, value: string) => {
          if (!value) return Promise.resolve();
          if (!formData.value.occupiedStartDate) {
            return Promise.reject(new Error('请先选择占用起始日期'));
          }
          const startDate = new Date(formData.value.occupiedStartDate);
          const endDate = new Date(value);
          if (endDate <= startDate) {
            return Promise.reject(new Error('占用结束日期必须大于占用起始日期'));
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    occupiedArea: [
      {
        validator: (rule: any, value: number) => {
          if ((formData.value.assetType === '0' || formData.value.assetType === '1') && (value === null || value === undefined || value === 0)) {
            return Promise.reject(new Error('请输入占用面积'));
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
    occupiedUserName: [{ required: true, message: '请输入占用人', trigger: 'blur' }],
    occupiedBookValue: [{ required: true, message: '请输入占用资产账面价值', trigger: 'blur' }],
    occupiedBookValueDate: [{ required: true, message: '请选择账面价值时点', trigger: 'change' }],
    changUse: [{ required: true, message: '请选择是否改变用途', trigger: 'change' }],
    illegal: [{ required: true, message: '请选择是否违建', trigger: 'change' }],
    occupiedReason: [{ required: true, message: '请输入占用原因', trigger: 'blur' }],
  };

  // 盘活记录表格列定义
  const dealColumns = [
    {
      title: '日期',
      key: 'revitalizeDate',
      width: 200,
    },
    {
      title: '是否已盘活',
      key: 'revitalized',
      width: 200,
    },
    {
      title: '盘活方式',
      key: 'revitalizeType',
      width: 200,
    },
    {
      title: '已采取的盘活管理措施',
      key: 'revitalizationMeasures',
      minWidth: 200,
    },
    {
      title: '下一步建议',
      key: 'strategicSuggestions',
      minWidth: 200,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
    },
  ];

  // 计算属性：状态控制
  const isDraftDisabled = computed(() => isUpdate.value);
  const isFiledDisabled = computed(() => {
    if (!isUpdate.value) return false;
    return originalStatus.value !== 0;
  });
  const isRevokedDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value !== 1;
  });
  const isVoidDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value === null || ![0, 2].includes(originalStatus.value);
  });

  // 监听资产类型变化，更新占用面积必填状态
  watch(
    () => formData.value.assetType,
    () => {
      // 触发占用面积字段的重新验证
      nextTick(() => {
        occupyFormRef.value?.validateFields(['occupiedArea']);
      });
    }
  );

  // 监听日期变化，自动计算占用天数
  watch([() => formData.value.occupiedStartDate, () => formData.value.occupiedEndDate], ([startDate, endDate]) => {
    calculateOccupyDays(startDate, endDate);
  });

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    const { id } = route.params;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
      await loadData(id as string);
    } else {
      isUpdate.value = false;
      // 设置默认值
      formData.value.inputTime = formatToDateTime(dayjs());
    }
  });

  // 加载数据（编辑时）
  async function loadData(id: string) {
    try {
      loading.value = true;
      const data = await getDetail(id);

      // 确保数据存在
      if (data) {
        // 处理数据，确保类型正确
        const processedData: FormData = {
          id: data.id?.toString() || '',
          supervisionCode: data.supervisionCode || '',
          assetType: data.assetType,
          assetId: data.assetId || '',
          assetCode: data.assetCode || '',
          occupiedAssetName: data.occupiedAssetName || '',
          manageCompanyName: data.manageCompanyName || '',
          reported: `${data.reported}`,
          operator: data.operator || '',
          inputUserName: data.inputUserName || '',
          inputTime: data.inputTime || '',
          status: data.status,
          occupiedStartDate: data.occupiedStartDate || '',
          occupiedEndDate: data.occupiedEndDate || '',
          occupiedDays: data.occupiedDays?.toString() || '',
          occupiedArea: data.occupiedArea,
          occupiedUserName: data.occupiedUserName || '',
          occupiedOriginalValue: data.occupiedOriginalValue,
          occupiedBookValue: data.occupiedBookValue,
          occupiedBookValueDate: data.occupiedBookValueDate || '',
          changUse: `${data.changUse}`,
          illegal: `${data.illegal}`,
          occupiedReason: data.occupiedReason || '',
          remark: data.remark || '',
          occupancyRevitalizationRecords: (data.occupancyRevitalizationRecords || []).map((item) => ({
            revitalizeDate: item.revitalizeDate || '',
            revitalized: `${item.revitalized}`,
            revitalizeType: `${item.revitalizeType}`,
            revitalizationMeasures: item.revitalizationMeasures || '',
            strategicSuggestions: item.strategicSuggestions || '',
          })),
        };

        processedData.initData = {
          value: data.assetId,
          label: data.occupiedAssetName + `(${data.assetCode})`,
          name: data.occupiedAssetName,
          manageUnitName: data.manageCompanyName,
        };

        originalStatus.value = data.status;

        formData.value = processedData;
        occupancyRevitalizationRecords.value = processedData.occupancyRevitalizationRecords || [];

        // createMessage.success('数据加载成功');
      } else {
        createMessage.error('未找到相关数据');
      }
    } catch (error) {
      console.error('加载占用信息详情失败:', error);
      createMessage.error('加载数据失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 重置表单
  function resetFields() {
    formData.value = {
      id: '',
      supervisionCode: '',
      assetType: '',
      assetId: '',
      assetCode: '',
      occupiedAssetName: '',
      manageCompanyName: '',
      reported: '',
      operator: '',
      inputUserName: userStore.getUserInfo.realname,
      inputTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      status: '',
      occupiedStartDate: '',
      occupiedEndDate: '',
      occupiedDays: '',
      occupiedArea: 0,
      occupiedUserName: '',
      occupiedOriginalValue: 0,
      occupiedBookValue: 0,
      occupiedBookValueDate: '',
      changUse: '',
      illegal: '',
      occupiedReason: '',
      remark: '',
      occupancyRevitalizationRecords: [],
    };
    occupancyRevitalizationRecords.value = [];
    occupyFormRef.value?.resetFields();
  }

  /**
   * 计算占用天数
   */
  function calculateOccupyDays(startDate?: string, endDate?: string) {
    if (!startDate) {
      formData.value.occupiedDays = '';
      return;
    }

    const start = new Date(startDate);
    let end: Date;

    if (endDate) {
      end = new Date(endDate);
    } else {
      end = new Date(); // 使用当前日期作为结束日期
    }

    // 计算天数差异
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    formData.value.occupiedDays = diffDays.toString();
  }

  /**
   * 添加盘活记录
   */
  function addDealRecord() {
    const newRecord: DealRecord = {
      revitalizeDate: '',
      revitalized: '',
      revitalizeType: '',
      revitalizationMeasures: '',
      strategicSuggestions: '',
    };
    occupancyRevitalizationRecords.value.push(newRecord);
    updateDealList();
  }

  const getPopupContainer = () => document.body;

  /**
   * 移除盘活记录
   */
  function removeDealRecord(index: number) {
    createConfirm({
      title: '确认删除',
      content: '确定要删除该盘活记录吗?',
      iconType: 'warning',
      onOk: () => {
        occupancyRevitalizationRecords.value.splice(index, 1);
        updateDealList();
        createMessage.success('删除成功!');
      },
    });
  }

  /**
   * 更新盘活记录列表
   */
  function updateDealList() {
    formData.value.occupancyRevitalizationRecords = [...occupancyRevitalizationRecords.value];
  }

  /**
   * 验证表单
   */
  async function validate() {
    try {
      await occupyFormRef.value?.validate();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取表单数据
   */
  function getFormData() {
    return {
      ...formData.value,
      occupancyRevitalizationRecords: occupancyRevitalizationRecords.value,
    };
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const isValid = await validate();
      if (!isValid) {
        return;
      }

      loading.value = true;

      const values = getFormData();

      // 如果是编辑，添加ID
      if (isUpdate.value) {
        values.id = recordId.value;
      }

      // 不需要设置manageUnit，已经使用manageCompanyName

      await saveOrUpdate(values);

      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      goBack();
    } catch (error) {
      console.error('提交失败:', error);
      createMessage.error('操作失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 重置表单
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '确定要重置表单吗？所有已填写的数据将会清空',
      iconType: 'warning',
      onOk: () => {
        resetFields();
        createMessage.success('表单已重置');
      },
    });
  }

  // 处理资产名称变更
  function handleAssetChange(value: string, option: any) {
    console.log(value, option, 'value, option');
    formData.value.assetCode = option.code || '';
    formData.value.manageCompanyName = option.manageUnitName || '';
    formData.value.occupiedAssetName = option.name || '';
  }

  // 处理资产类型变更
  function handleAssetTypeChange() {
    formData.value.assetId = '';
    formData.value.occupiedAssetName = '';
    formData.value.assetCode = '';
    formData.value.manageCompanyName = '';
  }

  // 日期禁用函数
  function startDateDisabled(current: any) {
    // 使用dayjs判断日期是否大于当前日期
    if (current && dayjs(current).isAfter(dayjs(), 'day')) {
      return true;
    }

    // 如果已经选择了结束日期，起始日期不能晚于结束日期
    if (formData.value.occupiedEndDate) {
      return dayjs(current).isAfter(dayjs(formData.value.occupiedEndDate), 'day');
    }

    return false;
  }

  function endDateDisabled(current: any) {
    if (!formData.value.occupiedStartDate) return false;
    const startDate = dayjs(formData.value.occupiedStartDate).valueOf();
    return startDate >= current.valueOf();
  }

  // 返回列表页
  function goBack() {
    router.push('/assetsUse/occupy');
  }
</script>

<style lang="less" scoped>
  .occupy-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        .form-card-action {
          display: flex;
          align-items: center;
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    // 盘活记录表格样式
    :deep(.ant-table-thead > tr > th) {
      background-color: #fafafa;
      font-weight: 500;
    }

    :deep(.ant-table-tbody > tr > td) {
      padding: 8px;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }
  }
</style>
