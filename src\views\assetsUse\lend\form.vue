<template>
  <div class="lend-form">
    <div class="p-4">
      <a-form :model="formData" :rules="rules" ref="lendFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="supervisionCode">
                  <template #label>
                    <span>序号</span>
                    <a-tooltip title="数据传到国资监管平台后将返回序号">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.supervisionCode" placeholder="序号为只读项" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetType" label="资产类型">
                  <JDictSelectTag
                    v-model:value="formData.assetType"
                    :showChooseOption="false"
                    dictCode="assets_type"
                    @change="handleAssetTypeChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetId" :label-col="{ span: 10 }" label="资产项目（资产名称）">
                  <AssetsSelect
                    v-model:value="formData.assetId"
                    :type="formData.assetType"
                    :disabled="!formData.assetType"
                    :initData="formData.initData"
                    @change="handleAssetProjectChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetCode" label="资产编号">
                  <a-input v-model:value="formData.assetCode" placeholder="选择资产项目后自动带出" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupiedAssetName" label="被占用资产名称">
                  <a-input v-model:value="formData.occupiedAssetName" placeholder="请输入被占用资产名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="manageCompanyName" label="管理单位">
                  <a-input v-model:value="formData.manageCompanyName" placeholder="选择资产项目后自动带出" disabled />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="reported" label="是否报送国资委">
                  <JDictSelectTag v-model:value="formData.reported" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="handlerUserName" label="经办人">
                  <a-input v-model:value="formData.handlerUserName" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="inputUserName" label="录入人">
                  <a-input v-model:value="formData.inputUserName" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="inputTime" label="录入时间">
                  <a-input v-model:value="formData.inputTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status">
                  <template #label>
                    <span>状态</span>
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option value="0" :disabled="isDraftDisabled">草稿</a-select-option>
                    <a-select-option value="1" :disabled="isFiledDisabled">备案</a-select-option>
                    <a-select-option value="2" :disabled="isRevokedDisabled">撤回</a-select-option>
                    <a-select-option value="4" :disabled="isVoidDisabled">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 借出信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:handshake-outlined" class="title-icon" />
              借出信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="loanStartDate" label="借出起始日期">
                  <a-date-picker
                    v-model:value="formData.loanStartDate"
                    placeholder="请选择借出起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="startDateDisabled"
                    @change="calculateLendDays"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="loanEndDate" label="借出结束日期">
                  <a-date-picker
                    v-model:value="formData.loanEndDate"
                    placeholder="请选择借出结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="endDateDisabled"
                    @change="calculateLendDays"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="loanDays" label="借出天数">
                  <a-input-number v-model:value="formData.loanDays" placeholder="自动计算" disabled style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="loanArea" label="借出面积（㎡）">
                  <a-input-number
                    v-model:value="formData.loanArea"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入借出面积"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="loanOriginalValue" :label-col="{ span: 10 }" label="借出资产原值（万元）">
                  <a-input-number
                    v-model:value="formData.loanOriginalValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入借出资产原值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="loanBookValue" :label-col="{ span: 10 }" label="借出资产账面价值（万元）">
                  <a-input-number
                    v-model:value="formData.loanBookValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入借出资产账面价值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="loanBookValueDate" label="账面价值时点">
                  <a-date-picker
                    v-model:value="formData.loanBookValueDate"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="loanUserName" label="借用人">
                  <a-input v-model:value="formData.loanUserName" placeholder="请输入借用人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="changUse" label="是否改变用途">
                  <JDictSelectTag v-model:value="formData.changUse" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="loanReason" :label-col="{ span: 2 }" label="借用原因">
                  <a-textarea v-model:value="formData.loanReason" placeholder="请输入借用原因" :rows="4" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="remark" :label-col="{ span: 2 }" label="备注">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="loading" style="margin-left: 12px"> 提交 </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="lend-info-form" setup>
  import { ref, onMounted, computed, watch, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { saveOrUpdate, getDetail } from './lend.api';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';
  import AssetsSelect from '/@/components/biz/select/assetsSelect.vue';
  import { JDictSelectTag } from '/@/components/Form';

  // 定义表单数据接口
  interface FormData {
    id: string;
    supervisionCode: string;
    assetType: string | null;
    assetCode: string;
    occupiedAssetName: string;
    manageCompanyName: string;
    reported: string | null;
    handlerUserName: string;
    inputUserName: string;
    inputTime: string;
    status: string | null;
    loanStartDate: string;
    loanEndDate: string;
    loanDays: number | null;
    loanArea: number | null;
    loanOriginalValue: number | null;
    loanBookValue: number | null;
    loanBookValueDate: string;
    loanUserName: string;
    changUse: string | null;
    loanReason: string;
    remark: string;
  }

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);
  const lendFormRef = ref();

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');
  const originalStatus = ref<string | null>(null);

  // 表单数据
  const formData = ref<FormData>({
    id: '',
    supervisionCode: '',
    assetType: null,
    assetCode: '',
    occupiedAssetName: '',
    manageCompanyName: '',
    reported: null,
    handlerUserName: '',
    inputUserName: userStore.getUserInfo.realname || '当前用户',
    inputTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    status: '',
    loanStartDate: '',
    loanEndDate: '',
    loanDays: null,
    loanArea: null,
    loanOriginalValue: null,
    loanBookValue: null,
    loanBookValueDate: '',
    loanUserName: '',
    changUse: '',
    loanReason: '',
    remark: '',
  });

  // 计算属性：状态控制
  const isDraftDisabled = computed(() => isUpdate.value);
  const isFiledDisabled = computed(() => {
    if (!isUpdate.value) return false;
    return originalStatus.value !== '0';
  });
  const isRevokedDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value !== '1';
  });
  const isVoidDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value === null || !['0', '2'].includes(originalStatus.value);
  });

  // 表单验证规则
  const rules = {
    // 基本信息验证规则
    assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
    reported: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    handlerUserName: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],

    // 借出信息验证规则
    loanStartDate: [{ required: true, message: '请选择借出起始日期', trigger: 'change' }],
    loanEndDate: [
      {
        validator: (rule: any, value: string) => {
          if (!value) return Promise.resolve();
          if (!formData.value.loanStartDate) {
            return Promise.reject(new Error('请先选择借出起始日期'));
          }
          const startDate = new Date(formData.value.loanStartDate);
          const endDate = new Date(value);
          if (endDate <= startDate) {
            return Promise.reject(new Error('借出结束日期必须大于借出起始日期'));
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    loanArea: [
      {
        validator: (rule: any, value: number) => {
          if ((formData.value.assetType === '0' || formData.value.assetType === '1') && (value === null || value === undefined || value === 0)) {
            return Promise.reject(new Error('请输入借出面积'));
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
    loanBookValue: [{ required: true, message: '请输入借出资产账面价值', trigger: 'blur' }],
    loanBookValueDate: [{ required: true, message: '请选择账面价值时点', trigger: 'change' }],
    loanUserName: [{ required: true, message: '请输入借用人', trigger: 'blur' }],
    changUse: [{ required: true, message: '请选择是否改变用途', trigger: 'change' }],
    loanReason: [{ required: true, message: '请输入借用原因', trigger: 'blur' }],
  };

  // 监听日期变化，自动计算借出天数
  watch(
    [() => formData.value.loanStartDate, () => formData.value.loanEndDate],
    ([startDate, endDate]) => {
      calculateLendDays(startDate, endDate);
    }
  );

  // 监听资产类型变化，更新借出面积必填状态
  watch(
    () => formData.value.assetType,
    () => {
      // 触发借出面积字段的重新验证
      nextTick(() => {
        lendFormRef.value?.validateFields(['loanArea']);
      });
    }
  );

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    const path = route.path;
    if (path.includes('/edit/')) {
      isUpdate.value = true;
      recordId.value = route.params.id as string;
      await loadData();
    } else {
      isUpdate.value = false;
      resetFields();
      // 设置默认值
      setDefaultValues();
    }
  });

  // 设置默认值
  function setDefaultValues() {
    formData.value.status = '0'; // 默认草稿状态
    formData.value.inputUserName = userStore.getUserInfo.realname || '当前用户'; // 录入人
    formData.value.inputTime = dayjs().format('YYYY-MM-DD HH:mm:ss'); // 录入时间
    formData.value.changUse = '1'; // 默认否
  }

  // 加载数据
  async function loadData() {
    try {
      const record = await getDetail(recordId.value);
      setFieldsValue(record);
      originalStatus.value = record.status;
    } catch (error) {
      createMessage.error('加载数据失败');
    }
  }

  // 设置表单值
  function setFieldsValue(data: any) {
    formData.value = {
      id: data.id || '',
      supervisionCode: data.supervisionCode || '',
      assetType: `${data.assetType}`,
      assetCode: data.assetCode || '',
      assetId: data.assetId || '',
      initData: {
        label: `${data.assetName}(${data.assetCode})`,
        name: data.assetName,
        value: data.assetId,
        code: data.assetCode,
        manageUnitName: data.manageCompanyName,
      },
      occupiedAssetName: data.occupiedAssetName || '',
      manageCompanyName: data.manageCompanyName || '',
      reported: `${data.reported}`,
      handlerUserName: data.handlerUserName || '',
      inputUserName: data.inputUserName || '',
      inputTime: data.inputTime || '',
      status: `${data.status}`,
      loanStartDate: data.loanStartDate || '',
      loanEndDate: data.loanEndDate || '',
      loanDays: data.loanDays,
      loanArea: data.loanArea,
      loanOriginalValue: data.loanOriginalValue,
      loanBookValue: data.loanBookValue,
      loanBookValueDate: data.loanBookValueDate || '',
      loanUserName: data.loanUserName || '',
      changUse: `${data.changUse}`,
      loanReason: data.loanReason || '',
      remark: data.remark || '',
    };
  }

  // 重置表单
  function resetFields() {
    lendFormRef.value?.resetFields();
    formData.value = {
      id: '',
      supervisionCode: '',
      assetType: null,
      assetCode: '',
      occupiedAssetName: '',
      manageCompanyName: '',
      reported: null,
      handlerUserName: '',
      inputUserName: userStore.getUserInfo.realname || '当前用户',
      inputTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      status: '',
      loanStartDate: '',
      loanEndDate: '',
      loanDays: null,
      loanArea: null,
      loanOriginalValue: null,
      loanBookValue: null,
      loanBookValueDate: '',
      loanUserName: '',
      changUse: '',
      loanReason: '',
      remark: '',
    };
  }

  // 计算借出天数
  function calculateLendDays(startDate: string, endDate: string) {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      formData.value.loanDays = diffDays;
    } else if (startDate) {
      const start = new Date(startDate);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      formData.value.loanDays = diffDays;
    } else {
      formData.value.loanDays = null;
    }
  }

  // 验证表单
  async function validate() {
    try {
      await lendFormRef.value?.validate();
      return true;
    } catch (error) {
      return false;
    }
  }

  // 获取表单数据
  function getFormData() {
    return { ...formData.value };
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const isValid = await validate();
      if (!isValid) {
        return;
      }

      loading.value = true;

      const submitData = getFormData();

      // 如果是编辑模式，添加ID
      if (isUpdate.value) {
        submitData.id = recordId.value;
      }

      await saveOrUpdate(submitData);
      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      router.push('/assetsUse/lend');
    } catch (error) {
      console.error('提交失败:', error);
      createMessage.error('操作失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 处理资产项目变更
  function handleAssetProjectChange(value: string, option: any) {
    formData.value.assetCode = option.code || '';
    formData.value.manageCompanyName = option.manageUnitName || '';
    formData.value.occupiedAssetName = option.name || '';
  }

  // 处理资产类型变更
  function handleAssetTypeChange() {
    formData.value.assetId = '';
    formData.value.occupiedAssetName = '';
    formData.value.assetCode = '';
    formData.value.manageCompanyName = '';
  }

  // 日期禁用函数
  function startDateDisabled(current: any) {
    // 使用dayjs判断日期是否大于当前日期
    if (current && dayjs(current).isAfter(dayjs(), 'day')) {
      return true;
    }

    // 如果已经选择了结束日期，起始日期不能晚于结束日期
    if (formData.value.loanEndDate) {
      return dayjs(current).isAfter(dayjs(formData.value.loanEndDate), 'day');
    }

    return false;
  }

  function endDateDisabled(current: any) {
    if (!formData.value.loanStartDate) return false;
    const startDate = dayjs(formData.value.loanStartDate).valueOf();
    return startDate >= current.valueOf();
  }

  // 重置
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '确定要重置表单吗？所有已填写的数据将会清空',
      iconType: 'warning',
      onOk: () => {
        resetFields();
        if (!isUpdate.value) {
          setDefaultValues();
        }
        createMessage.success('表单已重置');
      },
    });
  }
</script>

<style lang="less" scoped>
  .lend-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 提示图标样式
    .tooltip-icon {
      color: #909399;
      margin-left: 5px;
      cursor: pointer;
    }
  }
</style>