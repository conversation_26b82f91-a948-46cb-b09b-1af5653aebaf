<template>
  <div class="lend-form">
    <div class="simple-title">借出信息表单</div>
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
            基本信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerBasicForm" :schemas="basicInfoSchema" />
        </div>
      </div>

      <!-- 借出信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:handshake-outlined" class="title-icon" />
            借出信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerLendForm" :schemas="lendInfoSchema" />
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-footer">
        <a-button @click="handleReset">重置</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading" style="margin-left: 12px"> 提交 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="lend-info-form" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { basicInfoSchema, lendInfoSchema } from './lendForm.data';
  import { saveOrUpdate, getDetail } from './lend.api';

  const route = useRoute();
  const router = useRouter();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');
  const originalStatus = ref<number | null>(null);

  // 注册表单
  const [
    registerBasicForm,
    { resetFields: resetBasicFields, setFieldsValue: setBasicFieldsValue, validate: validateBasic, getFieldsValue: getBasicFieldsValue },
  ] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: basicInfoSchema,
  });

  const [
    registerLendForm,
    { resetFields: resetLendFields, setFieldsValue: setLendFieldsValue, validate: validateLend, getFieldsValue: getLendFieldsValue },
  ] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: lendInfoSchema,
  });

  // 计算属性：状态控制（暂时未使用，保留以备后续功能扩展）
  const _isDraftDisabled = computed(() => isUpdate.value);
  const _isFiledDisabled = computed(() => {
    if (!isUpdate.value) return false;
    return originalStatus.value !== 0;
  });
  const _isRevokedDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value !== 1;
  });
  const _isVoidDisabled = computed(() => {
    if (!isUpdate.value) return true;
    return originalStatus.value === null || ![0, 2].includes(originalStatus.value);
  });

  // 监听日期变化，自动计算借出天数
  watch(
    () => {
      const fields = getLendFieldsValue();
      return [fields?.loanStartDate, fields?.loanEndDate];
    },
    ([startDate, endDate]) => {
      calculateLendDays(startDate, endDate);
    }
  );

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    const path = route.path;
    if (path.includes('/edit/')) {
      isUpdate.value = true;
      recordId.value = route.params.id as string;
      await loadData();
    } else {
      isUpdate.value = false;
      resetFields();
      // 设置默认值
      setDefaultValues();
    }
  });

  // 设置默认值
  function setDefaultValues() {
    setBasicFieldsValue({
      status: 0, // 默认草稿状态
      inputUserName: '当前用户', // 录入人
      inputTime: new Date().toISOString().split('T')[0], // 录入时间
    });
    setLendFieldsValue({
      changUse: 1, // 默认否
    });
  }

  // 加载数据
  async function loadData() {
    try {
      const record = await getDetail(recordId.value);
      setFieldsValue(record);
      originalStatus.value = record.status;
    } catch (error) {
      createMessage.error('加载数据失败');
    }
  }

  // 设置表单值
  function setFieldsValue(data: any) {
    // 基本信息
    setBasicFieldsValue({
      id: data.id,
      supervisionCode: data.supervisionCode,
      assetType: data.assetType,
      assetProjectName: data.assetProjectName,
      assetCode: data.assetCode,
      occupiedAssetName: data.occupiedAssetName,
      manageCompanyName: data.manageCompanyName,
      reported: data.reported,
      handlerUserName: data.handlerUserName,
      inputUserName: data.inputUserName,
      inputTime: data.inputTime,
      status: data.status,
    });

    // 借出信息
    setLendFieldsValue({
      loanStartDate: data.loanStartDate,
      loanEndDate: data.loanEndDate,
      loanDays: data.loanDays,
      loanArea: data.loanArea,
      loanOriginalValue: data.loanOriginalValue,
      loanBookValue: data.loanBookValue,
      loanBookValueDate: data.loanBookValueDate,
      loanUserName: data.loanUserName,
      changUse: data.changUse,
      loanReason: data.loanReason,
      remark: data.remark,
    });
  }

  // 重置表单
  function resetFields() {
    resetBasicFields();
    resetLendFields();
  }

  // 计算借出天数
  function calculateLendDays(startDate: string, endDate: string) {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      setLendFieldsValue({ loanDays: diffDays });
    } else if (startDate) {
      const start = new Date(startDate);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      setLendFieldsValue({ loanDays: diffDays });
    } else {
      setLendFieldsValue({ loanDays: '' });
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      loading.value = true;

      // 验证所有表单
      const [basicData, lendData] = await Promise.all([validateBasic(), validateLend()]);

      // 合并数据
      const formData = {
        ...basicData,
        ...lendData,
      };

      // 如果是编辑模式，添加ID
      if (isUpdate.value) {
        formData.id = recordId.value;
      }

      await saveOrUpdate(formData, isUpdate.value);
      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      router.push('/assetsUse/lend');
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 重置
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '确定要重置表单吗？所有已填写的数据将会清空',
      onOk: () => {
        resetFields();
        if (!isUpdate.value) {
          setDefaultValues();
        }
        createMessage.success('表单已重置');
      },
    });
  }
</script>

<style lang="less" scoped>
  .lend-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }
  }
</style> 