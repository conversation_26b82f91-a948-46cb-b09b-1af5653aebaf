import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/biz/occupancy/page',
  save = '/biz/occupancy/save',
  delete = '/biz/occupancy/delete',

  importExcel = '/biz/occupancy/excel/import',
  exportXls = '/biz/occupancy/excel/export',
  downloadTemplate = '/biz/occupancy/template/excel/download',

  detail = '/biz/occupancy/detail',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params) => {
  const url = Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteOccupy = (params, handleSuccess) => {
  return defHttp.get({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 导出
 * @param params
 */
export const exportOccupy = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });
