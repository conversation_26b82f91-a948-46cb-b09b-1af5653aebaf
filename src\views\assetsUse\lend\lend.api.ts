import { defHttp } from '/@/utils/http/axios';

enum Api {
  LendList = '/biz/loan/page',
  LendSave = '/biz/loan/save',
  LendDelete = '/biz/loan/delete',
  LendDetail = '/biz/loan/detail',
  LendExport = '/biz/loan/excel/export',
  LendExportAll = '/biz/loan/excel/exportAll',
  LendImport = '/biz/loan/excel/import',
  LendTemplate = '/biz/loan/template/excel/download',
}

export const exportExcel = Api.LendExport;
export const downloadTemplate = Api.LendTemplate;
export const importExcel = Api.LendImport;

/**
 * 获取借出信息列表
 */
export const list = (params: any) => {
  return defHttp.post({ url: Api.LendList, params });
};

/**
 * 保存或更新借出信息
 */
export const saveOrUpdate = (params: any) => {
  return defHttp.post({ url: Api.LendSave, params });
};

/**
 * 删除借出信息
 */
export const deleteLend = (params: any) => {
  return defHttp.get({ url: Api.LendDelete, params });
};

/**
 * 获取借出信息详情
 */
export const getDetail = (id: string) => {
  return defHttp.get({ url: `${Api.LendDetail}`, params: { id } });
};
