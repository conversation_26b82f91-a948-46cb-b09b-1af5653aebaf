<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:import-outlined" @click="handleImport">导入</a-button> -->
        <import-modal @success="importSuccess" exportTemplateName="下载导入模板" :exportTemplateUrl="downloadTemplate" :importUrl="importExcel" />
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleExport"
          :disabled="selectedRowKeys.length === 0">导出</a-button>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

  </div>
</template>

<script lang="ts" name="occupy-list" setup>
  import { ref } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import { columns, searchFormSchema } from './occupy.data';
  import { list, deleteOccupy, exportExcel, importExcel, downloadTemplate } from './occupy.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  import { useMethods } from '/@/hooks/system/useMethods';
  import ImportModal from '/@/components/ImportModal/index.vue';

  const { createMessage } = useMessage();

  const router = useRouter();
  const exportLoading = ref(false);
  const { handleExportXls } = useMethods();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'occupy-list',
    tableProps: {
      title: '占用信息列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      showIndexColumn: true,
      showTableSetting: true,
      tableSetting: {
        cacheKey: 'occupy_list',
        setting: true,
        redo: false,
        size: false,
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        showAdvancedButton: true,
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
        fieldMapToTime: [
          ['occupiedStartDate', ['minOccupiedStartDate', 'maxOccupiedStartDate'], 'YYYY-MM-DD'],
          ['occupiedEndDate', ['minOccupiedEndDate', 'maxOccupiedEndDate'], 'YYYY-MM-DD'],
          ['occupiedBookValueDate', ['minOccupiedBookValueDate', 'maxOccupiedBookValueDate'], 'YYYY-MM-DD'],
          ['inputTime', ['minInputTime', 'maxInputTime'], 'YYYY-MM-DD'],
        ],
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return params;
      },
      showSummary: true,
      summaryFunc: (data) => {
        const totals = mapTableTotalSummary(data, [
          'occupyDays',
          'occupyArea',
          'assetsAmount',
          'bookAmount',
        ]);

        Object.keys(totals).forEach((key) => {
          if (typeof totals[key] === 'number') {
            totals[key] = Number(totals[key].toFixed(2));
          }
        });

        return [totals];
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  function handleCreate() {
    router.push('/assetsUse/occupy/add');
  }

  async function handleEdit(record: Recordable) {
    router.push(`/assetsUse/occupy/edit/${record.id}`);
  }

  function importSuccess() {
    reload();
  }

  async function handleDelete(record: Recordable) {
    await deleteOccupy({ id: record.id }, reload);
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm().getFieldsValue();
    formData.ids = selectedRowKeys.value;
    exportLoading.value = true;
    await handleExportXls('占用信息列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    const formData = getForm().getFieldsValue();
    console.log(formData, 'formData');
    exportLoading.value = true;
    await handleExportXls('占用信息列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style> 