<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:import-outlined" @click="handleImport">导入</a-button> -->
        <import-modal
          @success="importSuccess"
          exportTemplateName="下载导入模板"
          :exportTemplateUrl="downloadTemplate"
          :importUrl="importExcel"
        />
        <a-button
          type="primary"
          preIcon="ant-design:export-outlined"
          :loading="exportLoading"
          @click="handleExport"
          :disabled="selectedRowKeys.length === 0"
          >导出</a-button
        >
        <a-button type="primary" preIcon="ant-design:download-outlined" :loading="exportLoading" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #summary>
        <a-table-summary>
          <!-- 当页合计 -->
          <a-table-summary-row>
            <a-table-summary-cell align="right" :index="0" :col-span="4">当前页合计</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="5" :col-span="14" />
            <a-table-summary-cell align="right" :index="20" :col-span="1">{{ sumMap.landPriceCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="21" :col-span="1">{{ sumMap.landArrearsCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="22" :col-span="1">{{ sumMap.landAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="23" :col-span="1">{{ sumMap.renttableCurrentArea }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="24" :col-span="1">{{ sumMap.propertyAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="25" :col-span="1">{{ sumMap.notPropertyAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="26" :col-span="1">{{ sumMap.assetsAmountCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="27" :col-span="1">{{ sumMap.bookAmountCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="28" :col-span="15" />
            <a-table-summary-cell align="right" :index="44" :col-span="1">{{ sumMap.idleAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="45" :col-span="1">{{ sumMap.useAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="46" :col-span="1">{{ sumMap.rentAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="47" :col-span="1">{{ sumMap.lendAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="48" :col-span="1">{{ sumMap.occupyAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="49" :col-span="1">{{ sumMap.sellAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="50" :col-span="1">{{ sumMap.otherAreaCurrentSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="51" :col-span="5" />
            <a-table-summary-cell align="right" :index="53" />
          </a-table-summary-row>
          <!-- 合计 -->
          <a-table-summary-row>
            <a-table-summary-cell align="right" :index="0" :col-span="4">合计</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="5" :col-span="14" />
            <a-table-summary-cell align="right" :index="20" :col-span="1">{{ sumMap.landPriceSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="21" :col-span="1">{{ sumMap.landArrearsSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="22" :col-span="1">{{ sumMap.landAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="23" :col-span="1">{{ sumMap.renttableAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="24" :col-span="1">{{ sumMap.propertyAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="25" :col-span="1">{{ sumMap.notPropertyAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="26" :col-span="1">{{ sumMap.assetsAmountSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="27" :col-span="1">{{ sumMap.bookAmountSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="28" :col-span="15" />
            <a-table-summary-cell align="right" :index="44" :col-span="1">{{ sumMap.idleAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="45" :col-span="1">{{ sumMap.useAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="46" :col-span="1">{{ sumMap.rentAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="47" :col-span="1">{{ sumMap.lendAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="48" :col-span="1">{{ sumMap.occupyAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="49" :col-span="1">{{ sumMap.sellAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="50" :col-span="1">{{ sumMap.otherAreaSum }}</a-table-summary-cell>
            <a-table-summary-cell align="right" :index="51" :col-span="5" />
            <a-table-summary-cell align="right" :index="53" :col-span="1" />
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </BasicTable>

    <!-- 导入模态框 -->
    <!-- <ImportModal @register="registerImportModal" @success="handleImportSuccess" /> -->
  </div>
</template>

<script lang="ts" name="land-list" setup>
  import { ref, h } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/utils/use/use-modal';
  import { columns, searchFormSchema } from './land.data';
  import { list, getSum, deleteLand, exportExcel, importExcel, downloadTemplate } from './land.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  import ImportModal from '/@/components/ImportModal/index.vue';
  import { useMethods } from '/@/hooks/system/useMethods';

  const { createMessage } = useMessage();
  const { createModal } = useModal();
  const { handleExportXls } = useMethods();

  const router = useRouter();
  const exportLoading = ref(false);
  const sumMap = ref({});

  // 导入模态框
  // const [registerImportModal, { openModal: openImportModal }] = useModal();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'land-list',
    tableProps: {
      title: '土地信息列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'land_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
        fieldMapToTime: [
          ['createTime', ['createTimeMin', 'createTimeMax'], 'YYYY-MM-DD'],
          ['updateTime', ['updateTimeMin', 'updateTimeMax'], 'YYYY-MM-DD'],
        ],
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        if (params.landArea) {
          const [min, max] = params.landArea.split(',');
          params.landAreaMin = min;
          params.landAreaMax = max;
        }
        if (params.assetsLocation) {
          console.log(params.assetsLocation, 'params.assetsLocation');
          const [province, city, area] = params.assetsLocation.split(',');
          params.province = province;
          params.city = city;
          params.area = area;
        }
        console.log(params, 'params');
        getSumHandle(params);
        return params;
      },
      afterFetch: (data, res) => {
        console.log('data', data);
        console.log('res', res);
        return data;
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { getForm, reload }, { rowSelection, selectedRowKeys }] = tableContext;

  function getSumHandle(params) {
    getSum(params).then((res) => {
      // console.log(res, 'res');
      sumMap.value = res;
    });
  }

  /**
   * 新增事件
   */
  function handleCreate() {
    router.push('/assetsInfo/land/add');
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    router.push(`/assetsInfo/land/edit/${record.id}`);
  }

  function importSuccess(res) {
    reload();
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteLand({ id: record.id }, reload);
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm().getFieldsValue();
    formData.ids = selectedRowKeys.value;
    exportLoading.value = true;
    await handleExportXls('土地信息列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    const formData = getForm().getFieldsValue();
    console.log(formData, 'formData');
    exportLoading.value = true;
    await handleExportXls('土地信息列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导入事件
   */
  function handleImport() {
    const modal = createModal({
      title: '导入土地资产',
      content: () => h(ImportModal, {}),
      bodyStyle: { overflow: 'hidden', maxHeight: '700px' },
      width: 700,
      async onOk() {
        modal.hide();
      },
    });
  }

  /**
   * 导入成功回调
   */
  // function handleImportSuccess() {
  //   reload();
  // }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style>
