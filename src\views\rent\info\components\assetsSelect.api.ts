import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  getAssetInfo = '/biz/rentInfo/getAssetInfo/',
  getNoticByName = '/rent/notice/getNoticByName/',
}

export const getAssetInfo = (params) => {
  return defHttp.get({ url: Api.getAssetInfo, params });
};

export const getNoticByName = (params) => {
  return defHttp.get({ url: Api.getNoticByName, params });
};
