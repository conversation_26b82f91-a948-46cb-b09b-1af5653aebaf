import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/biz/rentInfo/queryPage',
  save = '/biz/rentInfo/add',
  edit = '/biz/rentInfo/edit',
  delete = '/biz/rentInfo/delete',
  deleteBatch = '/mock/rentInfo/deleteBatch',
  importExcel = '/biz/rentInfo/excel/import',
  exportXls = '/biz/rentInfo/excel/export',
  exportAll = '/mock/rentInfo/exportAll',
  downloadTemplate = '/biz/rentInfo/downloadImportTemplate',
  detail = '//biz/rentInfo/detail/',
  sum = '/biz/rentInfo/querySum',

  getRentInfoAssetPackageInfo = '/biz/rentInfo/getAssetPackageInfo/',

  getNoticByName = '/rent/notice/getNoticByName/',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

export const getSum = (params) => defHttp.post({ url: Api.sum, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  const method = isUpdate ? 'put' : 'post';
  return defHttp[method]({ url: url, data: params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => {
  console.log('获取详情ID:', id);
  return defHttp.get({ url: Api.detail + id, params: { id } });
};

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteRentInfo = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete + `?id=${params.id}` }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteRentInfo = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 导出
 * @param params
 */
export const exportRentInfo = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });

/**
 * 全部导出
 * @param params
 */
export const exportAllRentInfo = (params) => defHttp.get({ url: Api.exportAll, params, responseType: 'blob' });

export const getRentInfoAssetPackageInfo = (params) => {
  return defHttp.get({
    url: Api.getRentInfoAssetPackageInfo,
    params,
  });
};

export const getNoticByName = (params) => {
  return defHttp.get({
    url: Api.getNoticByName,
    params,
  });
};
