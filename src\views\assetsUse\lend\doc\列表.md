

## 借出信息-分页列表查询


**接口地址**:`/jeecg-boot/biz/loan/page`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "assetCode": "",
  "assetProjectName": "",
  "assetType": "",
  "changUse": 0,
  "handlerUserName": "",
  "inputUserName": "",
  "loanUserName": "",
  "manageCompanyName": "",
  "maxCreateTime": "",
  "maxLoanArea": 0,
  "maxLoanBookValue": "",
  "maxLoanDays": 0,
  "maxLoanEndDate": "",
  "maxLoanStartDate": "",
  "maxUpdateTime": "",
  "minCreateTime": "",
  "minLoanArea": 0,
  "minLoanBookValue": "",
  "minLoanDays": 0,
  "minLoanEndDate": "",
  "minLoanStartDate": "",
  "minUpdateTime": "",
  "occupiedAssetName": "",
  "pageNo": 0,
  "pageSize": 0,
  "reported": 0,
  "status": 0
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|queryRo|queryRo|body|true|企业信息检索|企业信息检索|
|&emsp;&emsp;assetCode|资产编号||false|string||
|&emsp;&emsp;assetProjectName|资产项目名称||false|string||
|&emsp;&emsp;assetType|资产类型||false|string||
|&emsp;&emsp;changUse|是否改变用途:0-是,1-否||false|integer(int32)||
|&emsp;&emsp;handlerUserName|经办人||false|string||
|&emsp;&emsp;inputUserName|录入人||false|string||
|&emsp;&emsp;loanUserName|借用人||false|string||
|&emsp;&emsp;manageCompanyName|管理单位名称||false|string||
|&emsp;&emsp;maxCreateTime|创建时间-最大||false|string(date-time)||
|&emsp;&emsp;maxLoanArea|借出面积-最大||false|number||
|&emsp;&emsp;maxLoanBookValue|借出资产账面价值（万元）-最大||false|string||
|&emsp;&emsp;maxLoanDays|借出天数-最大||false|integer(int32)||
|&emsp;&emsp;maxLoanEndDate|借出结束日期-最大||false|string(date-time)||
|&emsp;&emsp;maxLoanStartDate|借出起始日期-最大||false|string(date-time)||
|&emsp;&emsp;maxUpdateTime|更新时间-最大||false|string(date-time)||
|&emsp;&emsp;minCreateTime|创建时间-最小||false|string(date-time)||
|&emsp;&emsp;minLoanArea|借出面积-最小||false|number||
|&emsp;&emsp;minLoanBookValue|借出资产账面价值（万元）-最小||false|string||
|&emsp;&emsp;minLoanDays|借出天数-最小||false|integer(int32)||
|&emsp;&emsp;minLoanEndDate|借出结束日期-最小||false|string(date-time)||
|&emsp;&emsp;minLoanStartDate|借出起始日期-最小||false|string(date-time)||
|&emsp;&emsp;minUpdateTime|更新时间-最小||false|string(date-time)||
|&emsp;&emsp;occupiedAssetName|被占用资产名称||false|string||
|&emsp;&emsp;pageNo|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;reported|是否报送国资委:0-报送,1-不报送||false|integer(int32)||
|&emsp;&emsp;status|状态:草稿,备案,撤回,作废||false|integer(int32)||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«IPage«借出信息表单»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|IPage«借出信息表单»|IPage«借出信息表单»|
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|&emsp;&emsp;records||array|借出信息表单|
|&emsp;&emsp;&emsp;&emsp;assetCode|基础信息-资产编号|string||
|&emsp;&emsp;&emsp;&emsp;assetProjectName|基础信息-资产项目名称|string||
|&emsp;&emsp;&emsp;&emsp;assetType|基础信息-资产类型（枚举）|string||
|&emsp;&emsp;&emsp;&emsp;changUse|借出信息-是否改变用途:0-是,1-否|integer||
|&emsp;&emsp;&emsp;&emsp;createBy|创建人|string||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string||
|&emsp;&emsp;&emsp;&emsp;handlerUserName|基础信息-经办人|string||
|&emsp;&emsp;&emsp;&emsp;id|ID|integer||
|&emsp;&emsp;&emsp;&emsp;inputTime|基础信息-录入时间|string||
|&emsp;&emsp;&emsp;&emsp;inputUserName|基础信息-录入人|string||
|&emsp;&emsp;&emsp;&emsp;loanArea|借出信息-借出面积|number||
|&emsp;&emsp;&emsp;&emsp;loanBookValue|借出信息-借出资产账面价值（万元）|string||
|&emsp;&emsp;&emsp;&emsp;loanBookValueDate|借出信息-账面价值时点|string||
|&emsp;&emsp;&emsp;&emsp;loanDays|借出信息-借出天数|integer||
|&emsp;&emsp;&emsp;&emsp;loanEndDate|借出信息-借出结束日期|string||
|&emsp;&emsp;&emsp;&emsp;loanOriginalValue|借出信息-借出资产源值（万元）|string||
|&emsp;&emsp;&emsp;&emsp;loanReason|借出信息-借用原因|string||
|&emsp;&emsp;&emsp;&emsp;loanStartDate|借出信息-借出起始日期|string||
|&emsp;&emsp;&emsp;&emsp;loanUserName|借出信息-借用人|string||
|&emsp;&emsp;&emsp;&emsp;manageCompanyCode|基础信息-管理单位唯一编码|string||
|&emsp;&emsp;&emsp;&emsp;manageCompanyName|基础信息-管理单位名称|string||
|&emsp;&emsp;&emsp;&emsp;occupiedAssetName|基础信息-被占用资产名称|string||
|&emsp;&emsp;&emsp;&emsp;remark|借出信息-备注|string||
|&emsp;&emsp;&emsp;&emsp;reported|基础信息-是否报送国资委:0-报送,1-不报送|integer||
|&emsp;&emsp;&emsp;&emsp;status|基础信息-状态（枚举）|integer||
|&emsp;&emsp;&emsp;&emsp;supervisionCode|基础信息-序号:数据传到国资监管平台后将返回序号|string||
|&emsp;&emsp;&emsp;&emsp;updateBy|更新人|string||
|&emsp;&emsp;&emsp;&emsp;updateTime|更新时间|string||
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": {
		"current": 0,
		"pages": 0,
		"records": [
			{
				"assetCode": "",
				"assetProjectName": "",
				"assetType": "",
				"changUse": 0,
				"createBy": "",
				"createTime": "",
				"handlerUserName": "",
				"id": 0,
				"inputTime": "",
				"inputUserName": "",
				"loanArea": 0,
				"loanBookValue": "",
				"loanBookValueDate": "",
				"loanDays": 0,
				"loanEndDate": "",
				"loanOriginalValue": "",
				"loanReason": "",
				"loanStartDate": "",
				"loanUserName": "",
				"manageCompanyCode": "",
				"manageCompanyName": "",
				"occupiedAssetName": "",
				"remark": "",
				"reported": 0,
				"status": 0,
				"supervisionCode": "",
				"updateBy": "",
				"updateTime": ""
			}
		],
		"size": 0,
		"total": 0
	},
	"success": true,
	"timestamp": 0
}
```