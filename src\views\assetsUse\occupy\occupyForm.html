<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta name="prototype-page-id" content="occupyForm"> <!-- 添加此行指定当前页面ID -->
    <script src="annotationsData.js" defer></script>
    <script src="prototypeAnnotations.js" defer></script>

    <title>占用信息表单</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://demo.axureux.com/fontawesome/5.7.2/pro/css/all.min.css">
    <!-- 引入ElementUI组件库 -->
    <link rel="stylesheet" href="assets/element-ui/index.css">
    <style>
        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #f5222d;
            --font-size-base: 14px;
            --heading-color: rgba(0, 0, 0, 0.85);
            --text-color: rgba(0, 0, 0, 0.65);
            --disabled-color: rgba(0, 0, 0, 0.25);
            --border-color-base: #d9d9d9;
            --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-size: var(--font-size-base);
            color: var(--text-color);
            background-color: #f5f7fa;
            line-height: 1.5;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }

        .page-header {
            margin-bottom: 20px;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color-base);
        }

        .page-title {
            font-size: 24px;
            color: var(--heading-color);
            font-weight: 500;
        }

        .form-card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: var(--box-shadow-base);
            margin-bottom: 24px;
        }

        .form-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-base);
            background-color: #fafafa;
            border-radius: 4px 4px 0 0;
        }

        .form-card-title {
            font-size: 16px;
            color: var(--heading-color);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .form-card-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .form-card-body {
            padding: 24px;
        }

        .form-footer {
            text-align: center;
            padding: 24px 0;
        }

        .required:before {
            content: "*";
            color: var(--error-color);
            margin-right: 4px;
        }

        /* 自定义ElementUI样式 */
        .el-form-item {
            margin-bottom: 22px;
        }

        .el-textarea__inner {
            min-height: 120px !important;
        }

        .el-form-item__label {
            font-weight: 500;
        }

        .el-button--primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .el-button--primary:hover,
        .el-button--primary:focus {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .el-date-editor.el-input {
            width: 100%;
        }

        .el-select {
            width: 100%;
        }

        .el-input-number {
            width: 100%;
        }

        /* 提示图标样式 */
        .tooltip-icon {
            color: #909399;
            margin-left: 5px;
            cursor: pointer;
        }

        /* 去除Vue初始化闪烁 */
        [v-cloak] {
            display: none;
        }

        /* 盘活记录表格样式 */
        .table-container {
            margin-top: 16px;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        /* 表格中输入框对齐样式 */
        .record-table-form-item .el-form-item__content {
            line-height: normal;
            width: 100%;
        }

        .record-table-form-item.el-form-item {
            margin-bottom: 0;
        }

        .el-table .el-form-item {
            display: flex;
            margin-bottom: 0;
        }

        .el-table .el-form-item__content {
            flex: 1;
            margin-left: 0 !important;
        }

        .el-table .cell {
            padding: 8px 10px;
        }

        .empty-text {
            text-align: center;
            color: #909399;
            padding: 30px 0;
        }

        /* 帮助文本样式 */
        .help-text {
            font-size: 12px;
            color: #909399;
            line-height: 1.5;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">占用信息表单</h1>
            </div>

            <el-form :model="formData" :rules="rules" ref="occupyForm" label-width="180px" size="small">
                <!-- 基本信息 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="id">
                                    <template slot="label">
                                        <span>序号</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('数据传到国资监管平台后将返回序号')"></i>
                                    </template>
                                    <el-input v-model="formData.id" placeholder="序号为只读项" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="type">
                                    <template slot="label">
                                        <span>资产类型</span>
                                    </template>
                                    <el-select v-model="formData.type" placeholder="请选择资产类型" @change="handleAssetTypeChange">
                                        <el-option :key="0" label="土地" :value="0"></el-option>
                                        <el-option :key="1" label="房屋" :value="1"></el-option>
                                        <el-option :key="2" label="设备" :value="2"></el-option>
                                        <el-option :key="3" label="广告位" :value="3"></el-option>
                                        <el-option :key="4" label="其他" :value="4"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="name">
                                    <template slot="label">
                                        <span>资产项目（资产名称）</span>
                                    </template>
                                    <el-select
                                        v-model="formData.name"
                                        filterable
                                        remote
                                        reserve-keyword
                                        placeholder="请选择资产项目（资产名称）"
                                        :remote-method="remoteAssetSearch"
                                        :loading="loading"
                                        @change="handleAssetNameChange">
                                        <el-option
                                            v-for="item in assetOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="code">
                                    <template slot="label">
                                        <span>资产编号</span>
                                    </template>
                                    <el-input v-model="formData.code" placeholder="选择资产项目（资产名称）后自动带出" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <!-- 被占用资产名称 -->
                            <el-col :span="8">
                                <el-form-item prop="occupyName">
                                    <template slot="label">
                                        <span>被占用资产名称</span>
                                    </template>
                                    <el-input v-model="formData.occupyName" placeholder="请输入被占用资产名称"></el-input>
                                </el-form-item>
                            </el-col>
                            <!-- 管理单位 -->
                            <el-col :span="8">
                                <el-form-item prop="manageUnit">
                                    <template slot="label">
                                        <span>管理单位</span>
                                    </template>
                                    <el-input v-model="formData.manageUnit" placeholder="选择资产项目（资产名称）后自动带出" disabled></el-input>
                                    <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                                </el-form-item>
                            </el-col>
                            <!-- 是否报送国资委 -->
                            <el-col :span="8">
                                <el-form-item prop="reportOrNot">
                                    <template slot="label">
                                        <span>是否报送国资委</span>
                                    </template>
                                    <el-select v-model="formData.reportOrNot" placeholder="请选择">
                                        <el-option label="否" :value="0"></el-option>
                                        <el-option label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <!-- 经办人 -->
                            <el-col :span="8">
                                <el-form-item prop="operator">
                                    <template slot="label">
                                        <span>经办人</span>
                                    </template>
                                    <el-input v-model="formData.operator" placeholder="请输入经办人"></el-input>
                                </el-form-item>
                            </el-col>
                            <!-- 录入人 -->
                            <el-col :span="8">
                                <el-form-item prop="entryClerk">
                                    <template slot="label">
                                        <span>录入人</span>
                                    </template>
                                    <el-input v-model="formData.entryClerk" placeholder="录入人" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <!-- 录入时间 -->
                            <el-col :span="8">
                                <el-form-item prop="createTime">
                                    <template slot="label">
                                        <span>录入时间</span>
                                    </template>
                                    <el-input v-model="formData.createTime" placeholder="录入时间" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="status">
                                    <template slot="label">
                                        <span>状态</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('备案数据支持撤回、草稿数据和撤回数据支持作废')"></i>
                                    </template>
                                    <el-select v-model="formData.status" placeholder="请选择状态">
                                        <el-option :key="0" label="草稿" :value="0" :disabled="isDraftDisabled"></el-option>
                                        <el-option :key="1" label="备案" :value="1" :disabled="isFiledDisabled"></el-option>
                                        <el-option :key="2" label="撤回" :value="2" :disabled="isRevokedDisabled"></el-option>
                                        <el-option :key="4" label="作废" :value="4" :disabled="isVoidDisabled"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>

                <!-- 占用信息 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-user-check"></i> 占用信息
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="startDate">
                                    <template slot="label">
                                        <span>占用起始日期</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.startDate"
                                        type="date"
                                        placeholder="请选择占用起始日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="startDateOptions"
                                        @change="calculateOccupyDays"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="endDate">
                                    <template slot="label">
                                        <span>占用结束日期</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.endDate"
                                        type="date"
                                        placeholder="请选择占用结束日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="endDateOptions"
                                        @change="calculateOccupyDays"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="占用天数">
                                    <el-input v-model="formData.occupyDays" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="occupyArea" :rules="[{ required: formData.type === 0 || formData.type === 1, message: '请输入占用面积（㎡）', trigger: 'blur' }]">
                                    <template slot="label">
                                        <span>占用面积（㎡）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.occupyArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入占用面积"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="occupyPerson">
                                    <template slot="label">
                                        <span>占用人</span>
                                    </template>
                                    <el-input v-model="formData.occupyPerson" placeholder="请输入占用人"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="assetsAmount">
                                    <template slot="label">
                                        <span>占用资产原值（万元）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.assetsAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入占用资产原值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="bookAmount">
                                    <template slot="label">
                                        <span>占用资产账面价值（万元）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.bookAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入占用资产账面价值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="dateOfBookValue">
                                    <template slot="label">
                                        <span>账面价值时点</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.dateOfBookValue"
                                        type="date"
                                        placeholder="请选择账面价值时点"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="isChangePurpose">
                                    <template slot="label">
                                        <span>是否改变用途</span>
                                    </template>
                                    <el-select v-model="formData.isChangePurpose" placeholder="请选择是否改变用途">
                                        <el-option :key="0" label="否" :value="0"></el-option>
                                        <el-option :key="1" label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="isIllegalConstruction">
                                    <template slot="label">
                                        <span>是否违建</span>
                                    </template>
                                    <el-select v-model="formData.isIllegalConstruction" placeholder="请选择是否违建">
                                        <el-option :key="0" label="否" :value="0"></el-option>
                                        <el-option :key="1" label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item prop="occupyReason">
                                    <template slot="label">
                                        <span>占用原因</span>
                                    </template>
                                    <el-input 
                                        type="textarea" 
                                        v-model="formData.occupyReason" 
                                        placeholder="请输入占用原因" 
                                        :rows="4">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item prop="remark" :rules="[{ required: formData.dealStatus === 2, message: '请输入备注', trigger: 'blur' }]">
                                    <template slot="label">
                                        <span>备注</span>
                                    </template>
                                    <el-input 
                                        type="textarea" 
                                        v-model="formData.remark" 
                                        placeholder="请输入备注" 
                                        :rows="4">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>

                <!-- 盘活记录 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-sync-alt"></i> 盘活记录
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-table
                            :data="formData.dealList"
                            border
                            style="width: 100%">
                            <el-table-column prop="date" min-width="150">
                                <template slot="header">
                                    <span style="color:#f5222d">*</span> 日期
                                </template>
                                <template slot-scope="scope">
                                    <el-form-item 
                                        :prop="'dealList.' + scope.$index + '.date'"
                                        class="record-table-form-item">
                                        <el-date-picker
                                            v-model="scope.row.date"
                                            type="date"
                                            placeholder="请选择日期"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            style="width: 100%;">
                                        </el-date-picker>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <!-- 是否已盘活 -->
                            <el-table-column prop="isResult" min-width="110">
                                <template slot="header">
                                    <span style="color:#f5222d">*</span> 是否已盘活
                                </template>
                                <template slot-scope="scope">
                                    <el-form-item :prop="'dealList.' + scope.$index + '.isResult'" class="record-table-form-item" :rules="[{ required: true, message: '请选择是否已盘活', trigger: 'change' }]">
                                        <el-select v-model="scope.row.isResult" placeholder="请选择">
                                            <el-option label="否" :value="0"></el-option>
                                            <el-option label="是" :value="1"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <!-- 盘活方式 -->
                            <el-table-column prop="vitalizeType" min-width="130">
                                <template slot="header">
                                    <span style="color:#f5222d">*</span> 盘活方式
                                </template>
                                <template slot-scope="scope">
                                    <el-form-item :prop="'dealList.' + scope.$index + '.vitalizeType'" class="record-table-form-item" :rules="[{ required: true, message: '请选择盘活方式', trigger: 'change' }]">
                                        <el-select v-model="scope.row.vitalizeType" placeholder="请选择盘活方式">
                                            <el-option label="出租" :value="0"></el-option>
                                            <el-option label="出售" :value="1"></el-option>
                                            <el-option label="资产证券化" :value="2"></el-option>
                                            <el-option label="收储" :value="3"></el-option>
                                            <el-option label="转为自用" :value="4"></el-option>
                                            <el-option label="转为借用" :value="5"></el-option>
                                            <el-option label="转为占用" :value="6"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="reason" label="已采取的盘活管理措施" min-width="300">
                                <template slot-scope="scope">
                                    <el-form-item 
                                        :prop="'dealList.' + scope.$index + '.reason'"
                                        class="record-table-form-item">
                                        <el-input 
                                            type="textarea" 
                                            v-model="scope.row.reason" 
                                            placeholder="请输入已采取的盘活管理措施" 
                                            :rows="2">
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="nextReason" label="下一步建议" min-width="300">
                                <template slot-scope="scope">
                                    <el-form-item 
                                        :prop="'dealList.' + scope.$index + '.nextReason'"
                                        class="record-table-form-item">
                                        <el-input 
                                            type="textarea" 
                                            v-model="scope.row.nextReason" 
                                            placeholder="请输入下一步建议" 
                                            :rows="2">
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="80" fixed="right">
                                <template slot-scope="scope">
                                    <el-button
                                        type="danger"
                                        size="mini"
                                        icon="el-icon-delete"
                                        circle
                                        @click="removeRecord(scope.$index)">
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div v-if="formData.dealList.length === 0" class="empty-text">
                            暂无盘活记录，请点击下方"新增盘活记录"按钮添加。
                        </div>
                        <div style="margin-top: 15px; text-align: center;">
                            <el-button type="primary" size="small" icon="el-icon-plus" @click="addRecord">新增盘活记录</el-button>
                        </div>
                    </div>
                </div>
                
                <!-- 表单提交按钮 -->
                <div class="form-footer">
                    <el-button @click="resetForm">重置</el-button>
                    <el-button type="primary" @click="submitForm">提交</el-button>
                </div>
            </el-form>
        </div>
    </div>

    <!-- 引入Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <!-- 引入ElementUI组件库 -->
    <script src="assets/element-ui/index.js"></script>
    <!-- 引入axios -->
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.21.1/axios.min.js"></script>

    <script>
        new Vue({
            el: '#app',
            data() {
                // 定义资产类型验证函数
                const validateOccupyArea = (rule, value, callback) => {
                    if ((this.formData.type === 0 || this.formData.type === 1) && (value === '' || value === null || value === undefined)) {
                        callback(new Error('请输入占用面积'));
                    } else {
                        callback();
                    }
                };
                
                // 定义日期验证函数
                const validateEndDate = (rule, value, callback) => {
                    if (!value) {
                        callback();
                        return;
                    }
                    
                    if (!this.formData.startDate) {
                        callback(new Error('请先选择占用起始日期'));
                        return;
                    }
                    
                    const startDate = new Date(this.formData.startDate);
                    const endDate = new Date(value);
                    
                    if (endDate <= startDate) {
                        callback(new Error('占用结束日期必须大于占用起始日期'));
                    } else {
                        callback();
                    }
                };
                
                return {
                    loading: false,
                    isEditMode: false,
                    originalStatus: null,
                    // 表单数据
                    formData: {
                        // 基本信息
                        id: '', // 序号，系统生成
                        type: '', // 资产类型
                        name: '', // 资产名称
                        code: '', // 资产编号
                        occupyName: '', // 被占用资产名称
                        manageUnit: '', // 管理单位
                        reportOrNot: '', // 是否报送国资委
                        operator: '', // 经办人
                        entryClerk: '', // 录入人
                        createTime: '', // 录入时间
                        status: 0, // 状态，默认草稿
                        
                        // 占用信息
                        startDate: '', // 占用起始日期
                        endDate: '', // 占用结束日期
                        occupyDays: '', // 占用天数（计算得出）
                        occupyArea: '', // 占用面积
                        occupyPerson: '', // 占用人
                        assetsAmount: '', // 占用资产原值
                        bookAmount: '', // 占用资产账面价值
                        dateOfBookValue: '', // 账面价值时点
                        isChangePurpose: 0, // 是否改变用途，默认否
                        isIllegalConstruction: 0, // 是否违建，默认否
                        occupyReason: '', // 占用原因
                        remark: '', // 备注
                        
                        // 盘活记录
                        dealList: [], // 盘活记录列表
                    },
                    
                    // 表单验证规则
                    rules: {
                        // 基本信息验证规则
                        id: [
                            // { required: true, message: '序号不能为空', trigger: 'blur' } // 移除校验，该字段为后台返回
                        ],
                        type: [
                            { required: true, message: '请选择资产类型', trigger: 'change' }
                        ],
                        name: [
                            { required: true, message: '请选择资产项目（资产名称）', trigger: 'change' }
                        ],
                        code: [
                            { required: true, message: '资产编号不能为空', trigger: 'blur' }
                        ],
                        manageUnit: [
                            { required: true, message: '管理单位不能为空', trigger: 'change' }
                        ],
                        reportOrNot: [
                            { required: true, message: '请选择是否报送国资委', trigger: 'change' }
                        ],
                        operator: [
                            { required: true, message: '请输入经办人', trigger: 'blur' }
                        ],
                        entryClerk: [
                            { required: true, message: '录入人不能为空', trigger: 'blur' }
                        ],
                        createTime: [
                            { required: true, message: '录入时间不能为空', trigger: 'blur' }
                        ],
                        status: [
                            { required: true, message: '请选择状态', trigger: 'change' }
                        ],
                        
                        // 占用信息验证规则
                        startDate: [
                            { required: true, message: '请选择占用起始日期', trigger: 'change' }
                        ],
                        endDate: [
                            { validator: validateEndDate, trigger: 'change' }
                        ],
                        occupyArea: [
                            { validator: validateOccupyArea, trigger: 'blur' }
                        ],
                        occupyPerson: [
                            { required: true, message: '请输入占用人', trigger: 'blur' }
                        ],
                        bookAmount: [
                            { required: true, message: '请输入占用资产账面价值', trigger: 'blur' }
                        ],
                        dateOfBookValue: [
                            { required: true, message: '请选择账面价值时点', trigger: 'change' }
                        ],
                        isChangePurpose: [
                            { required: true, message: '请选择是否改变用途', trigger: 'change' }
                        ],
                        isIllegalConstruction: [
                            { required: true, message: '请选择是否违建', trigger: 'change' }
                        ],
                        occupyReason: [
                            { required: true, message: '请输入占用原因', trigger: 'blur' }
                        ],
                    },
                    
                    // 资产选项列表
                    assetOptions: [],
                    
                    // 企业选项（管理单位/所属企业）
                    enterpriseOptions: [
                        { value: 0, label: '厦门市城市建设发展投资有限公司' },
                        { value: 1, label: '厦门市地热资源管理有限公司' },
                        { value: 2, label: '厦门兴地房屋征迁服务有限公司' },
                        { value: 3, label: '厦门地丰置业有限公司' },
                        { value: 4, label: '图智策划咨询（厦门）有限公司' },
                        { value: 5, label: '厦门市集众祥和物业管理有限公司' },
                        { value: 6, label: '厦门市人居乐业物业服务有限公司' }
                    ],
                    
                    // 日期选择器配置
                    startDateOptions: {
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    endDateOptions: {
                        disabledDate: (time) => {
                            if (!this.formData.startDate) {
                                return time.getTime() < Date.now() - 86400000;
                            }
                            return time.getTime() < new Date(this.formData.startDate).getTime();
                        }
                    }
                };
            },
            computed: {
                isDraftDisabled() {
                    // 新建时可选，编辑时不可选
                    return this.isEditMode;
                },
                isFiledDisabled() {
                    // 新建时可选，编辑时只有草稿状态才能变更为备案
                    if (!this.isEditMode) return false;
                    return this.originalStatus !== 0;
                },
                isRevokedDisabled() {
                    // 新建时不可选，编辑时只有备案状态才能撤回
                    if (!this.isEditMode) return true;
                    return this.originalStatus !== 1;
                },
                isVoidDisabled() {
                    // 新建时不可选，编辑时只有草稿和撤回状态才能作废
                    if (!this.isEditMode) return true;
                    return ![0, 2].includes(this.originalStatus);
                }
            },
            methods: {
                // 资产类型变更处理
                handleAssetTypeChange() {
                    // 清空资产名称和编号
                    this.formData.name = '';
                    this.formData.code = '';
                    this.assetOptions = [];
                    
                    // 重新验证表单
                    this.$nextTick(() => {
                        this.$refs.occupyForm.validateField('occupyArea');
                    });
                },
                
                // 资产名称变更处理
                handleAssetNameChange(value) {
                    if (value) {
                        // 根据选择的资产名称，找到对应的资产编号和管理单位
                        const selectedAsset = this.assetOptions.find(item => item.value === value);
                        if (selectedAsset) {
                            this.formData.code = selectedAsset.code || '';
                            this.formData.manageUnit = selectedAsset.manageUnit || '';
                        }
                    } else {
                        this.formData.code = '';
                        this.formData.manageUnit = '';
                    }
                    this.$refs.occupyForm.validateField('manageUnit');
                },
                
                // 远程搜索资产
                remoteAssetSearch(query) {
                    if (query === '') return;
                    
                    this.loading = true;
                    
                    // 模拟搜索资产的API调用
                    setTimeout(() => {
                        this.loading = false;
                        
                        // 根据资产类型返回不同的搜索结果
                        const assetType = this.formData.type;
                        if (assetType === '') {
                            this.$message.warning('请先选择资产类型');
                            return;
                        }
                        
                        // 模拟数据
                        const typeMap = {
                            0: '土地',
                            1: '房屋',
                            2: '设备',
                            3: '广告位',
                            4: '其他'
                        };
                        
                        const enterpriseOptions = this.enterpriseOptions;
                        const mockData = [];
                        for (let i = 1; i <= 5; i++) {
                            const randomEnterprise = enterpriseOptions[Math.floor(Math.random() * enterpriseOptions.length)];
                            mockData.push({
                                label: `${typeMap[assetType]}${i}${query}`,
                                value: `${typeMap[assetType]}${i}${query}`,
                                code: `${assetType}000${i}`,
                                manageUnit: randomEnterprise.label
                            });
                        }
                        
                        this.assetOptions = mockData;
                    }, 500);
                },
                
                // 计算占用天数
                calculateOccupyDays() {
                    if (this.formData.startDate && this.formData.endDate) {
                        const startDate = new Date(this.formData.startDate);
                        const endDate = new Date(this.formData.endDate);
                        
                        // 计算相差的天数
                        const diffTime = Math.abs(endDate - startDate);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        
                        this.formData.occupyDays = diffDays;
                    } else if (this.formData.startDate) {
                        // 如果只有开始日期，则计算至今的天数
                        const startDate = new Date(this.formData.startDate);
                        const today = new Date();
                        
                        const diffTime = Math.abs(today - startDate);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        
                        this.formData.occupyDays = diffDays;
                    } else {
                        this.formData.occupyDays = '';
                    }
                },
                
                // 显示提示信息
                showTip(message) {
                    this.$message({
                        message: message,
                        type: 'info'
                    });
                },
                
                // 添加盘活记录
                addRecord() {
                    this.formData.dealList.push({
                        date: '',
                        isResult: '',
                        vitalizeType: '',
                        reason: '',
                        nextReason: ''
                    });
                },
                
                // 删除盘活记录
                removeRecord(index) {
                    this.$confirm('确定要删除该盘活记录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.formData.dealList.splice(index, 1);
                        this.$message({
                            type: 'success',
                            message: '删除成功！'
                        });
                    }).catch(() => {
                        // 取消删除操作
                    });
                },
                
                // 重置表单
                resetForm() {
                    this.$confirm('确定要重置表单吗？所有已填写的数据将会丢失。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$refs.occupyForm.resetFields();
                        this.formData.dealList = [];
                        this.$message({
                            type: 'success',
                            message: '表单已重置！'
                        });
                    }).catch(() => {
                        // 取消重置操作
                    });
                },
                
                // 提交表单
                submitForm() {
                    this.$refs.occupyForm.validate((valid) => {
                        if (valid) {
                            // 表单验证通过，准备提交数据
                            this.$message({
                                type: 'info',
                                message: '正在提交数据...'
                            });
                            
                            // 根据状态处理不同的提交逻辑
                            const statusMap = {
                                0: '草稿',
                                1: '备案',
                                2: '撤回',
                                4: '作废'
                            };
                            
                            // 模拟API调用
                            setTimeout(() => {
                                console.log('提交的表单数据：', this.formData);
                                
                                // 模拟成功提交
                                this.$message({
                                    type: 'success',
                                    message: `${statusMap[this.formData.status]}保存成功！`
                                });
                                
                                // 如果是备案，则禁用表单编辑
                                if (this.formData.status === 1) {
                                    // 实际项目中可能还需要重新加载表单或跳转页面
                                }
                            }, 1000);
                        } else {
                            this.$message({
                                type: 'error',
                                message: '表单验证失败，请检查并完善表单信息！'
                            });
                            return false;
                        }
                    });
                },
                // 格式化日期 yyyy-MM-dd HH:mm:ss
                formatDate(date, withTime = false) {
                    const d = new Date(date);
                    const year = d.getFullYear();
                    const month = (d.getMonth() + 1).toString().padStart(2, '0');
                    const day = d.getDate().toString().padStart(2, '0');
                    if (withTime) {
                        const hours = d.getHours().toString().padStart(2, '0');
                        const minutes = d.getMinutes().toString().padStart(2, '0');
                        const seconds = d.getSeconds().toString().padStart(2, '0');
                        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    }
                    return `${year}-${month}-${day}`;
                }
            },
            mounted() {
                const urlParams = new URLSearchParams(window.location.search);
                const id = urlParams.get('id');

                if (id) {
                    // 编辑模式
                    this.isEditMode = true;
                    const savedData = localStorage.getItem('occupyFormData');
                    if (savedData) {
                        this.formData = JSON.parse(savedData);
                        this.originalStatus = this.formData.status;

                        // 初始化远程搜索下拉框的选项
                        if (this.formData.name) {
                            this.assetOptions = [{
                                label: this.formData.name,
                                value: this.formData.name,
                                code: this.formData.code,
                                manageUnit: this.formData.manageUnit
                            }];
                        }
                    } else {
                        this.$message.error('未找到要编辑的数据，即将返回列表页。');
                        setTimeout(() => { window.location.href = 'occupyList.html'; }, 2000);
                    }
                } else {
                    // 新增模式
                    this.isEditMode = false;
                    // 设置默认用户和时间
                    const currentUser = '张三'; // 模拟当前登录用户
                    this.formData.operator = currentUser;
                    this.formData.entryClerk = currentUser;
                    this.formData.createTime = this.formatDate(new Date(), true);
                    this.formData.id = `ZY${new Date().getTime()}`; // 模拟生成临时ID
                }

                // 页面加载时自动计算占用天数（如果已有日期）
                this.calculateOccupyDays();
            }
        });
    </script>
</body>
</html>
