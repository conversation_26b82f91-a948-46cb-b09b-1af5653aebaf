import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '资产项目（资产名称）',
    dataIndex: 'assetProjectName',
    width: 200,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '资产编号',
    dataIndex: 'assetCode',
    width: 150,
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    width: 100,
    customRender: ({ text }) => {
      const typeMap = {
        0: '土地',
        1: '房屋',
        2: '设备',
        3: '广告位',
        4: '其他',
      };
      return typeMap[text] || '-';
    },
  },
  {
    title: '管理单位',
    dataIndex: 'manageCompanyName',
    width: 220,
    ellipsis: true,
  },
  {
    title: '被占用资产名称',
    dataIndex: 'occupiedAssetName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reported',
    width: 130,
    customRender: ({ text }) => {
      return text === 0 ? render.renderTag('是', 'success') : render.renderTag('否', 'default');
    },
  },
  {
    title: '借用人',
    dataIndex: 'loanUserName',
    width: 100,
  },
  {
    title: '借出起始日期',
    dataIndex: 'loanStartDate',
    width: 120,
  },
  {
    title: '借出结束日期',
    dataIndex: 'loanEndDate',
    width: 120,
  },
  {
    title: '借出天数',
    dataIndex: 'loanDays',
    width: 100,
    align: 'right',
  },
  {
    title: '借出面积(㎡)',
    dataIndex: 'loanArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '借出资产原值(万元)',
    dataIndex: 'loanOriginalValue',
    width: 140,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '借出资产账面价值(万元)',
    dataIndex: 'loanBookValue',
    width: 140,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值时点',
    dataIndex: 'loanBookValueDate',
    width: 120,
  },
  {
    title: '是否改变用途',
    dataIndex: 'changUse',
    width: 120,
    customRender: ({ text }) => {
      return text === 0 ? render.renderTag('是', 'success') : render.renderTag('否', 'default');
    },
  },
  {
    title: '借用原因',
    dataIndex: 'loanReason',
    width: 200,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '草稿', color: 'default' },
        1: { text: '备案', color: 'success' },
        2: { text: '撤回', color: 'warning' },
        4: { text: '作废', color: 'error' },
      };
      const status = statusMap[text];
      return status ? render.renderTag(status.text, status.color) : '';
    },
  },
  {
    title: '经办人',
    dataIndex: 'handlerUserName',
    width: 100,
  },
  {
    title: '录入人',
    dataIndex: 'inputUserName',
    width: 100,
  },
  {
    title: '录入时间',
    dataIndex: 'inputTime',
    width: 160,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 160,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'assetProjectName',
    label: '资产项目（资产名称）',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产项目（资产名称）',
    },
  },
  {
    field: 'assetCode',
    label: '资产编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'assetType',
    label: '资产类型',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择资产类型',
      options: [
        { label: '土地', value: 0 },
        { label: '房屋', value: 1 },
        { label: '设备', value: 2 },
        { label: '广告位', value: 3 },
        { label: '其他', value: 4 },
      ],
    },
  },
  {
    field: 'loanUserName',
    label: '借用人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入借用人',
    },
  },
  {
    field: 'manageCompanyName',
    label: '管理单位',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入管理单位名称',
    },
  },
  {
    field: 'occupiedAssetName',
    label: '被占用资产名称',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入被占用资产名称',
    },
  },
  {
    field: 'reported',
    label: '是否报送国资委',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '报送', value: 0 },
        { label: '不报送', value: 1 },
      ],
    },
  },
  {
    field: 'loanStartDateRange',
    label: '借出起始日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'loanEndDateRange',
    label: '借出结束日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'minLoanDays',
    label: '借出天数最小值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最少天数',
      style: { width: '100%' },
    },
  },
  {
    field: 'maxLoanDays',
    label: '借出天数最大值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最多天数',
      style: { width: '100%' },
    },
  },
  {
    field: 'minLoanArea',
    label: '借出面积最小值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小面积(㎡)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'maxLoanArea',
    label: '借出面积最大值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最大面积(㎡)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'minLoanBookValue',
    label: '账面价值最小值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小价值(万元)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'maxLoanBookValue',
    label: '账面价值最大值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最大价值(万元)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'changUse',
    label: '是否改变用途',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '是', value: 0 },
        { label: '否', value: 1 },
      ],
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '草稿', value: 0 },
        { label: '备案', value: 1 },
        { label: '撤回', value: 2 },
        { label: '作废', value: 4 },
      ],
    },
  },
  {
    field: 'handlerUserName',
    label: '经办人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'inputUserName',
    label: '录入人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'createTimeRange',
    label: '创建时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'updateTimeRange',
    label: '更新时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
]; 