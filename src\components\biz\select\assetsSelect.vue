<template>
  <a-select
    v-bind="getAttrs"
    placeholder="请选择关联公开招租"
    show-search
    :filter-option="false"
    :not-found-content="loading ? '加载中...' : null"
    @search="handleRelatedPublicLeasingSearch"
    @focus="() => handleRelatedPublicLeasingSearch('')"
    @change="handleChange"
    :options="assetsOptions"
  >
    <!-- <a-select-option v-for="item in assetsOptions" :key="item.value" :value="item.value">
      {{ item.label }}
    </a-select-option> -->
  </a-select>
</template>

<script lang="ts">
  import { defineComponent, ref, computed, watch } from 'vue';
  import { getAssetInfo } from './assetsSelect.api';
  import { useMessage } from '/@/hooks/web/useMessage';

  export default defineComponent({
    name: 'LinkAssetsModal',
    props: {
      type: {
        type: Object,
        default: () => ({}),
      },
      value: {
        type: String,
        default: '',
      },
      initData: {
        type: Object,
        default: () => null,
      },
    },
    emits: ['change', 'update:value'],
    setup(props, { attrs, emit }) {
      const loading = ref(false);
      const assetsOptions = ref([]);
      const init = ref(true);

      const getAttrs = computed(() => {
        return {
          value: props.value,
          ...attrs,
        };
      });

      // if (props.initData) {
      //   console.log('props.initData', props.initData);
      //   assetsOptions.value = [props.initData];
      // }

      function handleChange(value, option) {
        emit('change', value, option);
        emit('update:value', value, option);
      }

      function handleRelatedPublicLeasingSearch(value) {
        loading.value = true;
        console.log(props.value, 'init.value');
        if (init.value && props.initData) {
          init.value = false;
          assetsOptions.value = [props.initData];
          console.log(assetsOptions.value, 'assetsOptions.value');
          return;
        }
        getAssetInfo({ name: value, type: props.type })
          .then((res) => {
            console.log(res);
            assetsOptions.value = res.map((item) => ({ ...item, value: item.id, label: item.name + `(${item.code})` }));
          })
          .finally(() => {
            loading.value = false;
          });
      }

      watch(
        () => props.type,
        (newVal) => {
          if (newVal) {
            handleRelatedPublicLeasingSearch('');
          } else {
            assetsOptions.value = [];
          }
        },
        {
          immediate: true,
        }
      );

      return {
        getAttrs,
        assetsOptions,
        loading,
        handleRelatedPublicLeasingSearch,
        handleChange,
      };
    },
  });
</script>

<style lang="less" scoped>
</style> 