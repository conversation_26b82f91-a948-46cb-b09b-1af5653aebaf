# 是否启用mock
VITE_USE_MOCK = false

# 发布路径
VITE_PUBLIC_PATH = /

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用“,”分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

#后台接口父地址(必填)
VITE_GLOB_API_URL=http://***********:8080/jeecg-boot

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://***********:8080/jeecg-boot

# 接口父路径前缀
VITE_GLOB_API_URL_PREFIX=

VITE_GLOB_APP_OPEN_SSO = false

VITE_GLOB_APP_CAS_BASE_URL = http://**********
