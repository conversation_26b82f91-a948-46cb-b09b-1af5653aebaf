import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产项目（资产名称）',
    dataIndex: 'assetProject',
    width: 180,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '被占用资产名称',
    dataIndex: 'occupiedAssetName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '资产编号',
    dataIndex: 'assetCode',
    width: 150,
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'assets_type');
    },
  },
  {
    title: '管理单位',
    dataIndex: 'manageCompanyName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reported',
    width: 140,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '占用人',
    dataIndex: 'occupiedUserName',
    width: 100,
  },
  {
    title: '占用起始日期',
    dataIndex: 'occupiedStartDate',
    width: 120,
  },
  {
    title: '占用结束日期',
    dataIndex: 'occupiedEndDate',
    width: 120,
  },
  {
    title: '占用天数',
    dataIndex: 'occupiedDays',
    width: 100,
    align: 'right',
  },
  {
    title: '占用面积(㎡)',
    dataIndex: 'occupiedArea',
    width: 120,
    align: 'right',
  },
  {
    title: '占用资产原值(万元)',
    dataIndex: 'occupiedOriginalValue',
    width: 160,
    align: 'right',
  },
  {
    title: '占用资产账面价值(万元)',
    dataIndex: 'occupiedBookValue',
    width: 180,
    align: 'right',
  },
  {
    title: '账面价值时点',
    dataIndex: 'occupiedBookValueDate',
    width: 120,
  },
  {
    title: '是否改变用途',
    dataIndex: 'changUse',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否违建',
    dataIndex: 'illegal',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '占用原因',
    dataIndex: 'occupiedReason',
    width: 200,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'record_status');
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
  },
  {
    title: '经办人',
    dataIndex: 'handlerUserName',
    width: 100,
  },
  {
    title: '录入人',
    dataIndex: 'inputUserName',
    width: 100,
  },
  {
    title: '录入时间',
    dataIndex: 'inputTime',
    width: 160,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'assetProject',
    label: '资产项目',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产项目（资产名称）',
    },
  },
  {
    field: 'assetCode',
    label: '资产编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    field: 'assetType',
    label: '资产类型',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择资产类型',
      dictCode: 'assets_type',
    },
  },
  {
    field: 'manageCompanyName',
    label: '管理单位',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'reported',
    label: '是否报送国资委',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'handlerUserName',
    label: '经办人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'occupiedStartDate',
    label: '占用起始日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'occupiedEndDate',
    label: '占用结束日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'occupiedBookValueDate',
    label: '账面价值时点',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'inputTime',
    label: '录入时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'minOccupiedDays',
    label: '占用天数最小值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最少天数',
      style: { width: '100%' },
    },
  },
  {
    field: 'maxOccupiedDays',
    label: '占用天数最大值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最多天数',
      style: { width: '100%' },
    },
  },
  {
    field: 'minOccupiedArea',
    label: '占用面积最小值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小面积(㎡)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'maxOccupiedArea',
    label: '占用面积最大值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最大面积(㎡)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'minOccupiedBookValue',
    label: '账面价值最小值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小价值(万元)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'maxOccupiedBookValue',
    label: '账面价值最大值',
    component: 'InputNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最大价值(万元)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    field: 'changUse',
    label: '是否改变用途',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'illegal',
    label: '是否违建',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'occupiedAssetName',
    label: '被占用资产名称',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入被占用资产名称',
    },
  },
  {
    field: 'occupiedUserName',
    label: '占用人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入占用人',
    },
  },
];
