<template>
  <div class="vacant-form">
    <div class="p-4">
      <a-form :model="formData" :rules="rules" ref="vacantFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="id">
                  <template #label>
                    <span>序号</span>
                    <a-tooltip title="数据传到国资监管平台后将返回序号">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.id" placeholder="序号为只读项" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="type" label="资产类型">
                  <JDictSelectTag v-model:value="formData.type" :showChooseOption="false" dictCode="assets_type" @change="formData.name = ''" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetPackageId" :label-col="{ span: 8 }" label="资产项目（资产名称）">
                  <AssetsSelect
                    v-model:value="formData.assetPackageId"
                    :type="formData.type"
                    :disabled="!formData.type"
                    :initData="formData.initData"
                    @change="handleAssetChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="code" label="资产编号">
                  <a-input v-model:value="formData.code" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="idleName" label="闲置资产名称">
                  <a-input v-model:value="formData.idleName" placeholder="请输入闲置资产名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="manageUnit" label="管理单位">
                  <a-input v-model:value="formData.manageUnit" placeholder="选择资产项目（资产名称）后自动带出" disabled />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="reportOrNot" label="是否报送国资委">
                  <JDictSelectTag v-model:value="formData.reportOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="operator" label="经办人">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="entryClerk" label="录入人">
                  <a-input v-model:value="formData.entryClerk" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="createTime" label="录入时间">
                  <a-input v-model:value="formData.createTime" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status">
                  <template #label>
                    <span>状态</span>
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <Icon icon="ant-design:question-circle-outlined" class="tooltip-icon" />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option value="0" :disabled="initStatus === '1' || initStatus === '2' || initStatus === '4'">草稿</a-select-option>
                    <a-select-option value="1" :disabled="initStatus === '2' || initStatus === '4'">备案</a-select-option>
                    <a-select-option value="2" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '4'">撤回</a-select-option>
                    <a-select-option value="4" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '1'">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 空置信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:building-outlined" class="title-icon" />
              空置信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="startDate" label="空置起始日期">
                  <a-date-picker
                    v-model:value="formData.startDate"
                    placeholder="请选择空置起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="startDateDisabled"
                    @change="calculateIdleDays"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="endDate" label="空置结束日期">
                  <a-date-picker
                    v-model:value="formData.endDate"
                    placeholder="请选择空置结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="endDateDisabled"
                    @change="calculateIdleDays"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="空置天数">
                  <a-input v-model:value="formData.idleDays" disabled />
                  <div class="help-text">空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="idleArea" label="空置面积（㎡）">
                  <a-input-number
                    v-model:value="formData.idleArea"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入空置面积"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="assetsAmount" :label-col="{ span: 8 }" label="空置资产原值（万元）">
                  <a-input-number
                    v-model:value="formData.assetsAmount"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入空置资产原值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="bookAmount" :label-col="{ span: 10 }" label="空置资产账面价值（万元）">
                  <a-input-number
                    v-model:value="formData.bookAmount"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入空置资产账面价值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="dateOfBookValue" label="账面价值时点">
                  <a-date-picker
                    v-model:value="formData.dateOfBookValue"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="idleReason" :label-col="{ span: 2 }" label="空置原因">
                  <a-textarea v-model:value="formData.idleReason" placeholder="请输入空置原因" :rows="4" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item name="remark" :label-col="{ span: 2 }" label="备注">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 盘活记录 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:sync-outlined" class="title-icon" />
              盘活记录
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addDealRecord">
                <Icon icon="ant-design:plus-outlined" />
                新增盘活记录
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <a-table :columns="dealRecordColumns" :data-source="dealRecords" :pagination="false" :scroll="{ x: 1200 }" size="small" bordered>
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'dealDate'">
                  <a-form-item
                    :name="['dealList', index, 'dealDate']"
                    :rules="{ required: true, message: '请选择日期', trigger: 'change' }"
                    class="record-table-form-item"
                  >
                    <a-date-picker
                      v-model:value="record.dealDate"
                      placeholder="请选择日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'isResult'">
                  <a-form-item
                    :name="['dealList', index, 'isResult']"
                    :rules="{ required: true, message: '请选择是否已盘活', trigger: 'change' }"
                    class="record-table-form-item"
                  >
                    <JDictSelectTag
                      v-model:value="record.isResult"
                      :getPopupContainer="getPopupContainer"
                      :showChooseOption="false"
                      dictCode="yes_no"
                    />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'vitalizeType'">
                  <a-form-item
                    :name="['dealList', index, 'vitalizeType']"
                    :rules="{ required: true, message: '请选择盘活方式', trigger: 'change' }"
                    class="record-table-form-item"
                  >
                    <JDictSelectTag
                      v-model:value="record.vitalizeType"
                      :getPopupContainer="getPopupContainer"
                      :showChooseOption="false"
                      dictCode="vitalize_type"
                    />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'reason'">
                  <a-form-item :name="['dealList', index, 'reason']" class="record-table-form-item">
                    <a-textarea v-model:value="record.reason" placeholder="请输入已采取的盘活管理措施" :rows="2" />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'nextReason'">
                  <a-form-item :name="['dealList', index, 'nextReason']" class="record-table-form-item">
                    <a-textarea v-model:value="record.nextReason" placeholder="请输入下一步建议" :rows="2" />
                  </a-form-item>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-button type="link" danger size="small" @click="removeDealRecord(index)">
                    <Icon icon="ant-design:delete-outlined" />
                  </a-button>
                </template>
              </template>
            </a-table>
            <div v-if="dealRecords.length === 0" class="empty-text"> 暂无盘活记录，请点击上方"新增盘活记录"按钮添加。 </div>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="loading" style="margin-left: 12px">提交</a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="vacant-information-form" setup>
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { dealRecordColumns } from './vacantList.data';
  import { saveOrUpdate, getDetail } from './vacantList.api';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import AssetsSelect from '/@/components/biz/select/assetsSelect.vue';
  import { JAreaLinkage, JDictSelectTag, ApiSelect } from '/@/components/Form';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';

  // 定义盘活记录接口
  interface DealRecord {
    dealDate: string;
    isResult: string;
    vitalizeType: string;
    reason: string;
    nextReason: string;
  }

  // 定义表单数据接口
  interface FormData {
    id: string;
    type: string;
    name: string;
    code: string;
    idleName: string;
    manageUnit: string;
    reportOrNot: string;
    operator: string;
    entryClerk: string;
    createTime: string;
    status: string;
    startDate: string;
    endDate: string;
    idleDays: string;
    idleArea: number;
    assetsAmount: number;
    bookAmount: number;
    dateOfBookValue: string;
    idleReason: string;
    remark: string;
    dealList: DealRecord[];
  }

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);
  const vacantFormRef = ref();

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');
  const initStatus = ref('-1');

  // 表单数据
  const formData = ref<FormData>({
    // 基本信息
    id: '',
    type: '',
    name: '',
    code: '',
    idleName: '',
    manageUnit: '',
    reportOrNot: '',
    operator: '',
    entryClerk: userStore.getUserInfo.realname,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    status: '',
    // 空置信息
    startDate: '',
    endDate: '',
    idleDays: '',
    idleArea: 0,
    assetsAmount: 0,
    bookAmount: 0,
    dateOfBookValue: '',
    idleReason: '',
    remark: '',
    // 盘活记录
    dealList: [],
  });

  // 盘活记录数据
  const dealRecords = ref<DealRecord[]>([]);

  // 表单验证规则
  const rules = {
    // 基本信息验证规则
    type: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
    name: [{ required: true, message: '请选择资产项目（资产名称）', trigger: 'change' }],
    code: [{ required: true, message: '请选择资产项目（资产名称）后自动带出资产编号', trigger: 'change' }],
    manageUnit: [{ required: true, message: '管理单位为必填项', trigger: 'change' }],
    reportOrNot: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    operator: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    entryClerk: [{ required: true, message: '录入人为必填项' }],
    createTime: [{ required: true, message: '录入时间为必填项' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    idleName: [{ required: true, message: '请输入闲置资产名称', trigger: 'blur' }],

    // 空置信息验证规则
    startDate: [{ required: true, message: '请选择空置起始日期', trigger: 'change' }],
    endDate: [
      {
        validator: (rule: any, value: string) => {
          if (!value) return Promise.resolve();
          if (!formData.value.startDate) {
            return Promise.reject(new Error('请先选择空置起始日期'));
          }
          const startDate = new Date(formData.value.startDate);
          const endDate = new Date(value);
          if (endDate <= startDate) {
            return Promise.reject(new Error('空置结束日期必须大于空置起始日期'));
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    idleArea: [
      {
        validator: (rule: any, value: number) => {
          if ((formData.value.type === '0' || formData.value.type === '1') && (value === null || value === undefined || value === 0)) {
            return Promise.reject(new Error('请输入空置面积'));
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
    bookAmount: [{ required: true, message: '请输入空置资产账面价值', trigger: 'blur' }],
    dateOfBookValue: [{ required: true, message: '请选择账面价值时点', trigger: 'change' }],
    idleReason: [{ required: true, message: '请输入空置原因', trigger: 'blur' }],
  };

  onMounted(() => {
    const { id } = route.params;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
      loadData(id as string);
    } else {
      // 新增时设置默认值
      formData.value.createTime = formatToDateTime(dayjs());
    }
  });

  // 监听资产类型变化，更新空置面积必填状态
  watch(
    () => formData.value.type,
    () => {
      // 触发空置面积字段的重新验证
      nextTick(() => {
        vacantFormRef.value?.validateFields(['idleArea']);
      });
    }
  );

  // 监听日期变化，计算空置天数
  watch(
    [() => formData.value.startDate, () => formData.value.endDate],
    ([startDate, endDate]) => {
      calculateIdleDays(startDate, endDate);
    }
  );

  // 加载数据（编辑时）
  async function loadData(id: string) {
    try {
      loading.value = true;
      const data = await getDetail(id);

      // 确保数据存在
      if (data) {
        // 处理数据，确保类型正确
        const processedData: FormData = {
          id: data.id?.toString() || '',
          assetPackageId: data.assetPackageId || '',
          type: `${data.type}`,
          name: data.name || '',
          code: data.code || '',
          idleName: data.idleName || '',
          manageUnit: data.manageUnitName || '',
          reportOrNot: `${data.reportOrNot}`,
          operator: data.operator || '',
          entryClerk: data.entryClerk || '',
          createTime: data.createTime || '',
          status: `${data.status}`,
          startDate: data.startDate || '',
          endDate: data.endDate || '',
          idleDays: data.idleDays?.toString() || '',
          idleArea: data.idleArea,
          assetsAmount: data.assetsAmount,
          bookAmount: data.bookAmount,
          dateOfBookValue: data.dateOfBookValue || '',
          idleReason: data.idleReason || '',
          remark: data.remark || '',
          dealList: (data.dealList || []).map((item) => ({
            ...item,
            isResult: `${item.isResult}`,
            vitalizeType: `${item.vitalizeType}`,
          })),
        };

        processedData.initData = {
          value: data.assetPackageId,
          label: data.name + `(${data.code})`,
          name: data.name,
          manageUnitName: data.manageUnitName,
        };

        initStatus.value = `${data.status}`;

        formData.value = processedData;
        dealRecords.value = processedData.dealList || [];

        // createMessage.success('数据加载成功');
      } else {
        createMessage.error('未找到相关数据');
      }
    } catch (error) {
      console.error('加载空置信息详情失败:', error);
      createMessage.error('加载数据失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 计算空置天数
   */
  function calculateIdleDays(startDate: string, endDate: string) {
    if (!startDate) {
      formData.value.idleDays = '';
      return;
    }

    const start = new Date(startDate);
    let end;

    if (endDate) {
      end = new Date(endDate);
    } else {
      end = new Date(); // 使用当前日期作为结束日期
    }

    // 计算天数差异
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    formData.value.idleDays = diffDays.toString();
  }

  /**
   * 添加盘活记录
   */
  function addDealRecord() {
    const newRecord: DealRecord = {
      dealDate: '',
      isResult: '',
      vitalizeType: '',
      reason: '',
      nextReason: '',
      type: '',
    };
    dealRecords.value.push(newRecord);
    updateDealList();
  }

  /**
   * 移除盘活记录
   */
  function removeDealRecord(index: number) {
    createConfirm({
      title: '确认删除',
      content: '确定要删除该盘活记录吗?',
      iconType: 'warning',
      onOk: () => {
        dealRecords.value.splice(index, 1);
        updateDealList();
        createMessage.success('删除成功!');
      },
    });
  }

  /**
   * 更新盘活记录列表
   */
  function updateDealList() {
    formData.value.dealList = [...dealRecords.value];
  }

  /**
   * 验证表单
   */
  async function validate() {
    try {
      await vacantFormRef.value?.validate();

      return true;
    } catch (error) {
      return false;
    }
  }

  const getPopupContainer = () => document.body;

  /**
   * 获取表单数据
   */
  function getFormData() {
    return {
      ...formData.value,
      dealList: dealRecords.value,
    };
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const isValid = await validate();
      if (!isValid) {
        return;
      }

      loading.value = true;

      const values = getFormData();

      // 如果是编辑，添加ID
      if (isUpdate.value) {
        values.id = recordId.value;
      }

      values.manageUnit = '';

      await saveOrUpdate(values, isUpdate.value);

      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      goBack();
    } catch (error) {
      console.error('提交失败:', error);
      createMessage.error('操作失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 重置表单
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '确定要重置表单吗？所有已填写的数据将会清空',
      iconType: 'warning',
      onOk: () => {
        formData.value = {
          id: '',
          type: '',
          name: '',
          code: '',
          idleName: '',
          manageUnit: '',
          reportOrNot: '',
          operator: '',
          entryClerk: userStore.getUserInfo.realname,
          createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          status: '',
          startDate: '',
          endDate: '',
          idleDays: '',
          idleArea: 0,
          assetsAmount: 0,
          bookAmount: 0,
          dateOfBookValue: '',
          idleReason: '',
          remark: '',
          dealList: [],
        };
        dealRecords.value = [];
        vacantFormRef.value?.resetFields();
        createMessage.success('表单已重置');
      },
    });
  }

  // 处理资产名称变更
  function handleAssetChange(value: string, option: any) {
    console.log(value, option, 'value, option');
    formData.value.code = option.code || '';
    formData.value.manageUnit = option.manageUnitName || '';
    formData.value.idleName = option.name || '';
  }

  // 日期禁用函数
  function startDateDisabled(current: any) {
    // 使用dayjs判断日期是否大于当前日期
    if (current && dayjs(current).isAfter(dayjs(), 'day')) {
      return true;
    }

    // 如果已经选择了结束日期，起始日期不能晚于结束日期
    if (formData.value.endDate) {
      return dayjs(current).isAfter(dayjs(formData.value.endDate), 'day');
    }

    return false;
  }

  function endDateDisabled(current: any) {
    if (!formData.value.startDate) return false;
    const startDate = dayjs(formData.value.startDate).valueOf();
    return startDate >= current.valueOf();
  }

  // 返回列表页
  function goBack() {
    router.push('/idleInformation/vacantList');
  }
</script>

<style lang="less" scoped>
  .vacant-form {
    .form-card {
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      margin-bottom: 24px;
    }

    .form-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fafafa;
      border-radius: 6px 6px 0 0;
    }

    .form-card-title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      display: flex;
      align-items: center;

      .title-icon {
        margin-right: 8px;
        color: #1890ff;
        font-size: 18px;
      }
    }

    .form-card-action {
      .ant-btn {
        border-radius: 4px;
        font-weight: 500;
      }
    }

    .form-card-body {
      padding: 24px;

      .empty-text {
        text-align: center;
        color: #8c8c8c;
        padding: 40px 0;
        font-size: 14px;
        background: #fafafa;
        border-radius: 4px;
        border: 1px dashed #d9d9d9;
      }

      // 表格样式优化
      .ant-table {
        .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
          color: #262626;
          border-bottom: 2px solid #f0f0f0;
        }

        .ant-table-tbody > tr > td {
          border-bottom: 1px solid #f0f0f0;
        }

        .ant-table-tbody > tr:hover > td {
          background: #f5f5f5;
        }
      }
    }

    .form-footer {
      display: flex;
      justify-content: center;
      text-align: center;
      padding: 24px 0 8px 0;
      margin-top: 24px;
      border-top: 1px solid #f0f0f0;
    }

    // 表格内表单项样式
    .record-table-form-item {
      margin-bottom: 0;

      :deep(.ant-form-item-control) {
        line-height: normal;
        width: 100%;
      }
    }

    // 提示图标样式
    .tooltip-icon {
      color: #909399;
      margin-left: 5px;
      cursor: pointer;
    }

    // 帮助文本样式
    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 必填标记样式
    .required::before {
      content: '*';
      color: #ff4d4f;
      margin-right: 4px;
    }

    // 空状态样式
    .empty-text {
      text-align: center;
      color: #8c8c8c;
      padding: 40px 0;
      font-size: 14px;
      background: #fafafa;
      border-radius: 4px;
      border: 1px dashed #d9d9d9;
    }
  }

  .simple-title {
    font-size: 22px;
    font-weight: bold;
    margin: 24px 0 16px 0;
    color: #222;
    padding-left: 1em;
  }
</style>