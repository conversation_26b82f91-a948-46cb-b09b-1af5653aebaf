<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <import-modal @success="importSuccess" exportTemplateName="下载导入模板" :exportTemplateUrl="downloadTemplate" :importUrl="importExcel" />
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleExport" :disabled="selectedRowKeys.length === 0">导出</a-button>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="vacant-list" setup>
  import { ref, computed } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import { columns, searchFormSchema } from './vacantList.data';
  import { list, deleteVacant, exportExcel, importExcel, downloadTemplate, querySum } from './vacantList.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  import ImportModal from '/@/components/ImportModal/index.vue';
  import { useMethods } from '/@/hooks/system/useMethods';
  const { createMessage } = useMessage();

  const router = useRouter();
  const { handleExportXls } = useMethods();

  // 导入模态框
  const [registerImportModal, { openModal: openImportModal }] = useModal();

  const exportLoading = ref(false);

  const sumMap = ref({});

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'vacant-list',
    tableProps: {
      title: '空置信息列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: false,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'vacant_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
        fieldMapToTime: [
          ['createTime', ['createTimeMin', 'createTimeMax'], 'YYYY-MM-DD'],
          ['updateTime', ['updateTimeMin', 'updateTimeMax'], 'YYYY-MM-DD'],
        ],
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        if (params.idleDays) {
          const [min, max] = params.idleDays.split(',');
          params.idleDaysMin = min;
          params.idleDaysMax = max;
        }

        if (params.idleArea) {
          const [min, max] = params.idleArea.split(',');
          params.idleAreaMin = min;
          params.idleAreaMax = max;
        }

        if (params.idleArea) {
          const [min, max] = params.idleArea.split(',');
          params.bookAmountMin = min;
          params.bookAmountMax = max;
        }

        getSumHandle(params);
        return params;
      },
      // 显示合计行
      showSummary: true,
      summaryData: computed(() => {
        const totals = mapTableTotalSummary([], ['idleDays', 'idleArea', 'assetsAmount', 'bookAmount']);
        console.log('totals--------', totals);
        return [
          {
            ...totals,
            _row: '',
            _index: '',
            code: '当前页合计',
            idleDays: sumMap.value?.idleDaysCurrentSum,
            idleArea: sumMap.value?.idleAreaCurrentSum,
            assetsAmount: sumMap.value?.assetsAmountCurrentSum,
            bookAmount: sumMap.value?.bookAmountCurrentSum,
          },
          {
            ...totals,
            _row: '',
            _index: '',
            code: '合计',
            idleDays: sumMap.value?.idleDaysSum,
            idleArea: sumMap.value?.idleAreaSum,
            assetsAmount: sumMap.value?.assetsAmountSum,
            bookAmount: sumMap.value?.bookAmountSum,
          },
        ];
      }),
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  function importSuccess() {
    reload();
  }

  function getSumHandle(params) {
    querySum(params).then((res) => {
      // console.log(res, 'res');
      sumMap.value = res;
    });
  }


  /**
   * 新增事件
   */
  function handleCreate() {
    router.push('/idleInformation/vacantList/add');
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    router.push(`/idleInformation/vacantList/edit/${record.id}`);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteVacant({ id: record.id }, reload);
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm().getFieldsValue();
    formData.ids = selectedRowKeys.value;
    exportLoading.value = true;
    await handleExportXls('空置信息列表', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    const formData = getForm().getFieldsValue();
    exportLoading.value = true;
    await handleExportXls('空置信息列表', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导入事件
   */
  function handleImport() {
    openImportModal(true);
  }

  /**
   * 导入成功回调
   */
  function handleImportSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style>