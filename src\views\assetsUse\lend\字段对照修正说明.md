# 借出功能字段对照修正说明

## 修正概述

根据接口文档对借出功能的字段进行了全面对照和修正，确保前端代码与后端接口字段完全匹配。

## 主要修正内容

### 1. 列表显示字段修正 (lend.data.ts - columns)

| 原字段名 | 修正后字段名 | 说明 |
|---------|-------------|------|
| `name` | `assetProjectName` | 资产项目（资产名称） |
| `code` | `assetCode` | 资产编号 |
| `type` | `assetType` | 资产类型 |
| `manageUnit` | `manageCompanyName` | 管理单位名称 |
| `occupyName` | `occupiedAssetName` | 被占用资产名称 |
| `reportOrNot` | `reported` | 是否报送国资委 |
| `lendUser` | `loanUserName` | 借用人 |
| `startDate` | `loanStartDate` | 借出起始日期 |
| `endDate` | `loanEndDate` | 借出结束日期 |
| `lendDays` | `loanDays` | 借出天数 |
| `lendArea` | `loanArea` | 借出面积 |
| `assetsAmount` | `loanOriginalValue` | 借出资产原值 |
| `bookAmount` | `loanBookValue` | 借出资产账面价值 |
| `dateOfBookValue` | `loanBookValueDate` | 账面价值时点 |
| `changeUse` | `changUse` | 是否改变用途 |
| `lendReason` | `loanReason` | 借用原因 |
| `operator` | `handlerUserName` | 经办人 |
| `entryClerk` | `inputUserName` | 录入人 |
| `createTime` | `inputTime` | 录入时间 |

### 2. 搜索表单字段修正 (lend.data.ts - searchFormSchema)

| 原字段名 | 修正后字段名 | 说明 |
|---------|-------------|------|
| `name` | `assetProjectName` | 资产项目（资产名称） |
| `code` | `assetCode` | 资产编号 |
| `type` | `assetType` | 资产类型 |
| `lendUser` | `loanUserName` | 借用人 |
| `manageUnit` | `manageCompanyName` | 管理单位名称 |
| `occupyName` | `occupiedAssetName` | 被占用资产名称 |
| `reportOrNot` | `reported` | 是否报送国资委 |
| `startDateRange` | `loanStartDateRange` | 借出起始日期范围 |
| `endDateRange` | `loanEndDateRange` | 借出结束日期范围 |
| `minLendDays` | `minLoanDays` | 借出天数最小值 |
| `maxLendDays` | `maxLoanDays` | 借出天数最大值 |
| `minLendArea` | `minLoanArea` | 借出面积最小值 |
| `maxLendArea` | `maxLoanArea` | 借出面积最大值 |
| `minBookAmount` | `minLoanBookValue` | 账面价值最小值 |
| `maxBookAmount` | `maxLoanBookValue` | 账面价值最大值 |
| `changeUse` | `changUse` | 是否改变用途 |
| `operator` | `handlerUserName` | 经办人 |
| `entryClerk` | `inputUserName` | 录入人 |

### 3. 表单配置字段修正 (lendForm.data.ts)

#### 基本信息表单 (basicInfoSchema)
| 原字段名 | 修正后字段名 | 说明 |
|---------|-------------|------|
| `id` | `supervisionCode` | 序号 |
| `type` | `assetType` | 资产类型 |
| `name` | `assetProjectName` | 资产项目（资产名称） |
| `code` | `assetCode` | 资产编号 |
| `occupyName` | `occupiedAssetName` | 被占用资产名称 |
| `manageUnit` | `manageCompanyName` | 管理单位名称 |
| `reportOrNot` | `reported` | 是否报送国资委 |
| `operator` | `handlerUserName` | 经办人 |
| `entryClerk` | `inputUserName` | 录入人 |
| `createTime` | `inputTime` | 录入时间 |

#### 借出信息表单 (lendInfoSchema)
| 原字段名 | 修正后字段名 | 说明 |
|---------|-------------|------|
| `startDate` | `loanStartDate` | 借出起始日期 |
| `endDate` | `loanEndDate` | 借出结束日期 |
| `lendDays` | `loanDays` | 借出天数 |
| `lendArea` | `loanArea` | 借出面积 |
| `assetsAmount` | `loanOriginalValue` | 借出资产原值 |
| `bookAmount` | `loanBookValue` | 借出资产账面价值 |
| `dateOfBookValue` | `loanBookValueDate` | 账面价值时点 |
| `lendUser` | `loanUserName` | 借用人 |
| `changeUse` | `changUse` | 是否改变用途 |
| `lendReason` | `loanReason` | 借用原因 |

### 4. 表单页面字段修正 (form.vue)

修正了表单页面中的字段映射逻辑，包括：
- 日期监听字段：`startDate/endDate` → `loanStartDate/loanEndDate`
- 天数计算字段：`lendDays` → `loanDays`
- 默认值设置字段：`entryClerk/createTime/changeUse` → `inputUserName/inputTime/changUse`
- 表单值设置的所有字段映射

### 5. API接口修正 (lend.api.ts)

- 修正详情查询接口：使用路径参数 `/detail/{id}` 而不是查询参数
- 修正删除接口：使用 GET 请求而不是 DELETE 请求

### 6. 列表页面修正 (index.vue)

- 添加了日期范围字段的时间映射配置 `fieldMapToTime`
- 修正了合计字段名称
- 修正了删除函数的调用方式

### 7. 枚举值修正

#### 是否报送国资委 (reported)
- 接口定义：0-报送，1-不报送
- 修正了选项标签和值的对应关系

#### 是否改变用途 (changUse)
- 接口定义：0-是，1-否
- 修正了选项标签和值的对应关系，默认值改为1（否）

## 注意事项

1. **字段命名规范**：所有字段名称现在完全按照接口文档中的定义
2. **枚举值对应**：确保前端选项值与后端枚举值完全匹配
3. **日期格式**：统一使用 'YYYY-MM-DD' 格式
4. **数值精度**：金额字段保持2位小数精度
5. **必填验证**：保持原有的必填字段验证规则

## 测试建议

1. 测试列表查询功能，确保所有搜索条件正常工作
2. 测试新增/编辑功能，确保表单提交数据格式正确
3. 测试删除功能，确保接口调用正常
4. 测试导入/导出功能，确保字段映射正确
5. 验证枚举值显示是否正确（是否报送国资委、是否改变用途等）

## 完成状态

✅ 列表显示字段对照修正
✅ 搜索表单字段对照修正  
✅ 表单配置字段对照修正
✅ 表单页面逻辑修正
✅ API接口调用修正
✅ 枚举值对应关系修正
✅ 日期范围处理修正

所有字段对照修正已完成，代码现在与接口文档完全匹配。
