<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="导入借出信息"
    width="650px"
    :showCancelBtn="false"
    @ok="handleSubmit"
  >
    <div style="padding: 0 20px;">
      <div style="margin-bottom: 20px;">
        <a-alert
          message="请先下载导入模板，按照模板格式填写后上传"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div style="margin-bottom: 30px; text-align: center;">
        <a-button type="primary" @click="handleDownloadTemplate">
          <Icon icon="ant-design:download-outlined" style="margin-right: 5px;" />
          下载导入模板
        </a-button>
      </div>

      <a-upload
        v-model:file-list="fileList"
        name="file"
        :multiple="false"
        :action="uploadAction"
        :headers="uploadHeaders"
        :before-upload="beforeUpload"
        :show-upload-list="true"
        accept=".xlsx,.xls"
        :max-size="10 * 1024 * 1024"
      >
        <a-button>
          <Icon icon="ant-design:upload-outlined" />
          选择文件
        </a-button>
        <template #itemRender="{ file }">
          <span>{{ file.name }}</span>
          <a-button
            type="link"
            size="small"
            @click="removeFile(file)"
            style="margin-left: 8px;"
          >
            删除
          </a-button>
        </template>
      </a-upload>

      <div style="margin-top: 10px; color: #909399; font-size: 12px;">
        只能上传xlsx/xls文件，且不超过10MB
      </div>
    </div>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :disabled="!canSubmit">确定</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" name="lend-import-modal" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import { importLend, downloadTemplate } from './lend.api';
  import { getToken } from '/@/utils/auth';

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();

  const fileList = ref<any[]>([]);
  const canSubmit = ref(false);

  const uploadAction = '/api/lend/import';
  const uploadHeaders = {
    'X-Access-Token': getToken(),
  };

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    fileList.value = [];
    canSubmit.value = false;
  });

  /**
   * 下载模板
   */
  async function handleDownloadTemplate() {
    try {
      await downloadTemplate();
      createMessage.success('模板下载成功');
    } catch (error) {
      createMessage.error('模板下载失败');
    }
  }

  /**
   * 上传前验证
   */
  function beforeUpload(file: File) {
    const isExcel = file.type === 'application/vnd.ms-excel' || 
                    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isExcel) {
      createMessage.error('只能上传Excel文件!');
      return false;
    }

    if (!isLt10M) {
      createMessage.error('文件大小不能超过10MB!');
      return false;
    }

    canSubmit.value = true;
    return false; // 阻止自动上传
  }

  /**
   * 删除文件
   */
  function removeFile(file: any) {
    const index = fileList.value.findIndex((item) => item.uid === file.uid);
    if (index > -1) {
      fileList.value.splice(index, 1);
    }
    canSubmit.value = fileList.value.length > 0;
  }

  /**
   * 提交导入
   */
  async function handleSubmit() {
    if (fileList.value.length === 0) {
      createMessage.warning('请先上传文件');
      return;
    }

    setModalProps({ confirmLoading: true });

    try {
      const formData = new FormData();
      formData.append('file', fileList.value[0].originFileObj);

      await importLend(formData);
      createMessage.success('导入成功');
      emit('success');
      closeModal();
    } catch (error) {
      createMessage.error('导入失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<script lang="ts">
export default {
  name: 'ImportModal'
};
</script>