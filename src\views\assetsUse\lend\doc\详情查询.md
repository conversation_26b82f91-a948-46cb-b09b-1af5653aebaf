

## 借出信息-详情查询


**接口地址**:`/jeecg-boot/biz/loan/detail/{id}`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|id|query|true|string||
|X-Access-Token|token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|接口返回对象«借出信息表单»|
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|返回代码|integer(int32)|integer(int32)|
|message|返回处理消息|string||
|result|返回数据对象|借出信息表单|借出信息表单|
|&emsp;&emsp;assetCode|基础信息-资产编号|string||
|&emsp;&emsp;assetProjectName|基础信息-资产项目名称|string||
|&emsp;&emsp;assetType|基础信息-资产类型（枚举）|string||
|&emsp;&emsp;changUse|借出信息-是否改变用途:0-是,1-否|integer(int32)||
|&emsp;&emsp;createBy|创建人|string||
|&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;handlerUserName|基础信息-经办人|string||
|&emsp;&emsp;id|ID|integer(int32)||
|&emsp;&emsp;inputTime|基础信息-录入时间|string(date-time)||
|&emsp;&emsp;inputUserName|基础信息-录入人|string||
|&emsp;&emsp;loanArea|借出信息-借出面积|number||
|&emsp;&emsp;loanBookValue|借出信息-借出资产账面价值（万元）|string||
|&emsp;&emsp;loanBookValueDate|借出信息-账面价值时点|string(date-time)||
|&emsp;&emsp;loanDays|借出信息-借出天数|integer(int32)||
|&emsp;&emsp;loanEndDate|借出信息-借出结束日期|string(date-time)||
|&emsp;&emsp;loanOriginalValue|借出信息-借出资产源值（万元）|string||
|&emsp;&emsp;loanReason|借出信息-借用原因|string||
|&emsp;&emsp;loanStartDate|借出信息-借出起始日期|string(date-time)||
|&emsp;&emsp;loanUserName|借出信息-借用人|string||
|&emsp;&emsp;manageCompanyCode|基础信息-管理单位唯一编码|string||
|&emsp;&emsp;manageCompanyName|基础信息-管理单位名称|string||
|&emsp;&emsp;occupiedAssetName|基础信息-被占用资产名称|string||
|&emsp;&emsp;remark|借出信息-备注|string||
|&emsp;&emsp;reported|基础信息-是否报送国资委:0-报送,1-不报送|integer(int32)||
|&emsp;&emsp;status|基础信息-状态（枚举）|integer(int32)||
|&emsp;&emsp;supervisionCode|基础信息-序号:数据传到国资监管平台后将返回序号|string||
|&emsp;&emsp;updateBy|更新人|string||
|&emsp;&emsp;updateTime|更新时间|string(date-time)||
|success|成功标志|boolean||
|timestamp|时间戳|integer(int64)|integer(int64)|


**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"result": {
		"assetCode": "",
		"assetProjectName": "",
		"assetType": "",
		"changUse": 0,
		"createBy": "",
		"createTime": "",
		"handlerUserName": "",
		"id": 0,
		"inputTime": "",
		"inputUserName": "",
		"loanArea": 0,
		"loanBookValue": "",
		"loanBookValueDate": "",
		"loanDays": 0,
		"loanEndDate": "",
		"loanOriginalValue": "",
		"loanReason": "",
		"loanStartDate": "",
		"loanUserName": "",
		"manageCompanyCode": "",
		"manageCompanyName": "",
		"occupiedAssetName": "",
		"remark": "",
		"reported": 0,
		"status": 0,
		"supervisionCode": "",
		"updateBy": "",
		"updateTime": ""
	},
	"success": true,
	"timestamp": 0
}
```