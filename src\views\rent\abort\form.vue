<template>
  <div class="abort-form">
    <div class="simple-title">租赁中止表单</div>
    <div class="p-4">
      <!-- 租赁中止信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
            租赁中止信息
          </div>
        </div>
        <div class="form-card-body">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            :label-col="{ style: { width: '180px' } }"
            :wrapper-col="{ span: 16 }"
            label-align="right"
          >
            <a-row :gutter="20">
              <!-- 租赁资产包名称 -->
              <a-col :span="12">
                <a-form-item label="租赁资产包名称" name="assetPackageId" required>
                  <a-select
                    v-model:value="formData.assetPackageId"
                    placeholder="请选择租赁资产包名称"
                    :filter-option="false"
                    show-search
                    @change="handleAssetPackageChange"
                    :options="assetPackageOptions"
                    :loading="loadingSearch"
                    @search="handleSearch"
                  />
                </a-form-item>
              </a-col>

              <!-- 租赁资产包编号 -->
              <a-col :span="12">
                <a-form-item label="租赁资产包编号" name="code" required>
                  <a-input v-model:value="formData.code" placeholder="选择租赁资产包名称后自动带出" disabled />
                </a-form-item>
              </a-col>

              <!-- 管理单位 -->
              <a-col :span="12">
                <a-form-item label="管理单位" name="manageUnit" required>
                  <a-input v-model:value="formData.manageUnit" placeholder="选择租赁资产包后自动带出" disabled />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>

              <!-- 是否报送国资委 -->
              <a-col :span="12">
                <a-form-item label="是否报送国资委" name="reportOrNot" required>
                  <JDictSelectTag v-model:value="formData.reportOrNot" :showChooseOption="false" placeholder="是否报送国资委" dictCode="yes_no" />
                </a-form-item>
              </a-col>

              <!-- 经办人 -->
              <a-col :span="12">
                <a-form-item label="经办人" name="operator" required>
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>

              <!-- 录入人 -->
              <a-col :span="12">
                <a-form-item label="录入人" name="entryClerk" required>
                  <a-input v-model:value="formData.entryClerk" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>

              <!-- 录入时间 -->
              <a-col :span="12">
                <a-form-item label="录入时间" name="createTime" required>
                  <a-input v-model:value="formData.createTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>

              <!-- 是否成交 -->
              <a-col :span="12">
                <a-form-item label="是否成交" name="abortStatus" required>
                  <JDictSelectTag
                    v-model:value="formData.abortStatus"
                    :showChooseOption="false"
                    @change="handleAbortStatusChange"
                    placeholder="是否成交"
                    dictCode="yes_no"
                  />
                </a-form-item>
              </a-col>

              <!-- 出租方式 -->
              <a-col :span="12">
                <a-form-item label="出租方式" name="rentType" required>
                  <JDictSelectTag v-model:value="formData.rentType" :showChooseOption="false" placeholder="请选择出租方式" dictCode="rent_type" />
                </a-form-item>
              </a-col>

              <!-- 标的名称 (仅在成交时显示) -->
              <a-col :span="12" v-if="formData.abortStatus === '1'">
                <a-form-item label="标的名称" name="targetName" :required="formData.abortStatus === '1'">
                  <a-input v-model:value="formData.targetName" placeholder="请输入标的名称" />
                </a-form-item>
              </a-col>

              <!-- 中止日期 (仅在成交时显示) -->
              <a-col :span="12" v-if="formData.abortStatus === '1'">
                <a-form-item label="中止日期" name="abortDate" :required="formData.abortStatus === '1'">
                  <a-date-picker
                    v-model:value="formData.abortDate"
                    placeholder="请选择中止日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>

              <!-- 实收总租金 (仅在成交时显示) -->
              <a-col :span="12" v-if="formData.abortStatus === '1'">
                <a-form-item label="实收总租金（万元）" name="actTotalRent" :required="formData.abortStatus === '1'">
                  <a-input-number
                    v-model:value="formData.actTotalRent"
                    placeholder="请输入实收总租金"
                    :min="0"
                    :precision="2"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>

              <!-- 中止原因 (仅在成交时显示) -->
              <a-col :span="24" v-if="formData.abortStatus === '1'">
                <a-form-item label="中止原因" name="abortReason" :required="formData.abortStatus === '1'">
                  <a-textarea v-model:value="formData.abortReason" placeholder="请输入中止原因" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-footer">
        <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
        <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="AbortForm" setup>
  import { ref, onMounted, reactive } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { JAreaLinkage, JDictSelectTag, ApiSelect } from '/@/components/Form';
  import { saveOrUpdate, getDetail, getRentInfoAssetPackageInfo } from './abort.api';
  import { useUserStore } from '/@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import dayjs from 'dayjs';

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);
  const loadingSearch = ref(false);
  const formRef = ref<FormInstance>();

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');

  // 资产包选项
  const assetPackageOptions = ref<any[]>([]);

  // 表单数据
  const formData = reactive<any>({
    assetPackageId: undefined,
    code: undefined,
    manageUnit: undefined,
    reportOrNot: '',
    operator: '',
    entryClerk: userStore.getUserInfo.realname,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    abortStatus: undefined,
    rentType: undefined,
    targetName: undefined,
    abortDate: undefined,
    actTotalRent: undefined,
    abortReason: undefined,
  });

  // 表单验证规则
  const formRules = {
    assetPackageId: [{ required: true, message: '请选择租赁资产包名称', trigger: 'change' }],
    code: [{ required: true, message: '租赁资产包编号不能为空', trigger: 'blur' }],
    manageUnit: [{ required: true, message: '管理单位不能为空', trigger: 'blur' }],
    reportOrNot: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    operator: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    entryClerk: [{ required: true, message: '录入人不能为空', trigger: 'blur' }],
    createTime: [{ required: true, message: '录入时间不能为空', trigger: 'blur' }],
    abortStatus: [{ required: true, message: '请选择是否成交', trigger: 'change' }],
    rentType: [{ required: true, message: '请选择出租方式', trigger: 'change' }],
    targetName: [{ required: true, message: '请输入标的名称', trigger: 'blur' }],
    abortDate: [{ required: true, message: '请选择中止日期', trigger: 'change' }],
    actTotalRent: [{ required: true, message: '请输入实收总租金', trigger: 'blur' }],
    abortReason: [{ required: true, message: '请输入中止原因', trigger: 'blur' }],
  };

  // 资产包选择变化处理
  function handleAssetPackageChange(value: string, option: any) {
    console.log(value, option, 'value, option');
    if (option) {
      formData.code = option.code;
      formData.manageUnit = option.manageUnitName;
    }
  }

  // 成交状态变化处理
  function handleAbortStatusChange(value: number) {
    if (value === 0) {
      // 如果选择"否"，清空成交相关字段
      formData.targetName = undefined;
      formData.abortDate = undefined;
      formData.actTotalRent = undefined;
      formData.abortReason = undefined;
    }
  }

  // 禁用未来日期
  function disabledDate(current: any) {
    return current && current > new Date();
  }

  /**
   * 初始化
   */
  onMounted(async () => {

    // 判断是新增还是编辑
    const { id } = route.params;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
      await loadDetail();
    } else {
      isUpdate.value = false;
      handleSearch('');
      // 设置默认值已在 reactive 中定义
    }
  });

  /**
   * 加载详情
   */
  async function loadDetail() {
    try {
      const data = await getDetail(recordId.value);
      if (data) {
        Object.assign(formData, {
          ...data,
          manageUnit: data.manageUnitName,
          reportOrNot: `${data.reportOrNot}`,
          abortStatus: `${data.abortStatus}`,
          rentType: `${data.rentType}`,
        });
        assetPackageOptions.value = [
          {
            value: data.assetPackageId,
            label: data.name,
            code: data.code,
            manageUnit: data.manageUnitName,
          },
        ];
      }
    } catch (error) {
      createMessage.error('加载详情失败');
    }
  }

  // 搜索资产
  async function handleSearch(name: string) {
    loadingSearch.value = true;
    try {
      const res = await getRentInfoAssetPackageInfo({ name });
      const options = res.map((item) => ({ ...item, value: item.id, label: item.name }));
      assetPackageOptions.value = options;
    } catch (error) {
      console.error('获取资产包选项失败:', error);
    } finally {
      loadingSearch.value = false;
    }
  }

  /**
   * 提交表单
   */
  async function handleSubmit() {
    try {
      // 验证表单
      await formRef.value?.validate();

      // 如果是成交状态，需要额外验证相关字段
      if (formData.abortStatus === 1) {
        if (!formData.targetName || !formData.abortReason || !formData.abortDate || !formData.actTotalRent) {
          createMessage.error('成交状态下，标的名称、中止日期、实收总租金、中止原因为必填项');
          return;
        }
      }

      // 合并表单数据
      const submitData = {
        ...formData,
        id: isUpdate.value ? recordId.value : undefined,
      };

      delete submitData.manageUnit;

      loading.value = true;
      await saveOrUpdate(submitData, isUpdate.value);

      createMessage.success(isUpdate.value ? '更新成功' : '新增成功');
      router.push('/rent/abort');
    } catch (error) {
      createMessage.error(isUpdate.value ? '更新失败' : '新增失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 重置表单
   */
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '是否确认重置表单数据？',
      iconType: 'warning',
      onOk: () => {
        if (isUpdate.value) {
          loadDetail();
        } else {
          formRef.value?.resetFields();
          // 重新设置默认值
          Object.assign(formData, {
            assetPackageId: undefined,
            code: undefined,
            manageUnit: undefined,
            reportOrNot: 1,
            operator: '张三',
            entryClerk: '张三',
            createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
            abortStatus: undefined,
            rentType: undefined,
            targetName: undefined,
            abortDate: undefined,
            actTotalRent: undefined,
            abortReason: undefined,
          });
        }
      },
    });
  }
</script>
<style lang="less" scoped>
  .abort-form {
    .simple-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
      padding: 16px 24px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .form-card {
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .form-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        .form-card-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          color: #333;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 24px;
      }
    }

    .form-footer {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 统一设置表单项标签宽度
    :deep(.ant-form-item-label) {
      width: 180px;
      min-width: 180px;
      text-align: right;
      padding-right: 8px;
    }

    :deep(.ant-form-item-label > label) {
      width: 100%;
      justify-content: flex-end;
    }
  }
</style>
