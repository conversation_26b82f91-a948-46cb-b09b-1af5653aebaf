import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

enum Api {
  list = '/biz/transferinfo/page',
  save = '/biz/transferinfo/save',

  delete = '/biz/transferinfo/delete',
  detail = '/biz/transferinfo/detail',

  export = '/biz/transferinfo/excel/export',
  exportAll = '/mock/transferInfo/exportAll',
  import = '/biz/transferinfo/excel/import',

  downloadTemplate = '/biz/transferinfo/template/excel/download',
}

export const exportExcel = Api.export;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.import;

/**
 * 获取转让信息列表
 */
export const list = (params?: any) => {
  return defHttp.post<any>({
    url: Api.list,
    params,
  });
};

/**
 * 获取转让信息详情
 */
export const getDetail = (id: string) => {
  return defHttp.get<any>({
    url: Api.detail,
    params: { id },
  });
};

/**
 * 保存或更新转让信息
 */
export const saveOrUpdate = (params: any) => {
  return defHttp.post<any>({
    url: Api.save,
    params,
  });
};

/**
 * 删除转让信息
 */
export const deleteTransfer = (params: { id: string }) => {
  return defHttp.get<any>({
    url: Api.delete,
    params,
  });
};

/**
 * 导出选中的转让信息
 */
export const exportTransfer = (params: { ids: string[] }) => {
  return defHttp.post<any>(
    {
      url: Api.export,
      params,
      responseType: 'blob',
    },
    {
      successMessageMode: 'message',
      onSuccess: () => {
        createMessage.success('导出成功');
      },
    }
  );
};

/**
 * 导出全部转让信息
 */
export const exportAllTransfer = (params: any) => {
  return defHttp.post<any>(
    {
      url: Api.exportAll,
      params,
      responseType: 'blob',
    },
    {
      successMessageMode: 'message',
      onSuccess: () => {
        createMessage.success('导出成功');
      },
    }
  );
};

/**
 * 导入转让信息
 */
export const importTransfer = (params: any) => {
  return defHttp.post<any>(
    {
      url: Api.import,
      params,
    },
    {
      successMessageMode: 'message',
    }
  );
};

/**
 * 获取资产选项（用于关联资产选择）
 */
export const getAssetOptions = (params?: { keyword?: string; assetType?: number }) => {
  return defHttp.get<any>({
    url: '/mock/assets/options',
    params,
  });
};

/**
 * 获取转让方选项
 */
export const getTransferorOptions = (params?: { keyword?: string }) => {
  return defHttp.get<any>({
    url: '/mock/transferor/options',
    params,
  });
};
