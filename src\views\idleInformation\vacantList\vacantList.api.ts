import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/biz/assets/idle/queryPage',

  save = '/biz/assets/idle/add',
  edit = '/biz/assets/idle/edit',

  delete = '/biz/assets/idle/delete/',

  importExcel = '/biz/assets/idle/excel/import',
  exportXls = '/biz/assets/idle/excel/export',
  downloadTemplate = '/biz/assets/idle/downloadImportTemplate',

  detail = '/biz/assets/idle/info/',

  querySum = '/biz/assets/idle/querySum',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  const method = isUpdate ? 'put' : 'post';
  return defHttp[method]({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail + id, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteVacant = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete + params.id, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 获取合计
 * @param params
 */
export const querySum = (params) => defHttp.post({ url: Api.querySum, params });
