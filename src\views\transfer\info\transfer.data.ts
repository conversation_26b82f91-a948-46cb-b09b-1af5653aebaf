import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '资产包编号',
    dataIndex: 'transferPackageCode',
    width: 160,
    fixed: 'left',
  },
  {
    title: '转让资产包名称',
    dataIndex: 'transferPackageName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '转让方式',
    dataIndex: 'transferMethod',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'transfer_method');
    },
  },
  {
    title: '主要资产类型',
    dataIndex: 'mainAssetType',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'assets_type');
    },
  },
  {
    title: '管理单位',
    dataIndex: 'manageCompanyName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '挂牌价格(万元)',
    dataIndex: 'listingPrice',
    width: 130,
    align: 'right',
    customRender: ({ text, record }) => {
      if (record.relatedAssetsList && record.relatedAssetsList.length > 0) {
        const result = record.relatedAssetsList.map((item) => {
          return item.listingPrice;
        });
        return result.join(',');
      }
      return text;
    },
  },
  {
    title: '转让方名称',
    dataIndex: 'transferCompanyName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return render.renderDict(text, 'record_status');
    },
  },
  {
    title: '是否成交',
    dataIndex: 'dealStatus',
    width: 90,
    customRender: ({ text, record }) => {
      if (record.relatedDealInfoList && record.relatedDealInfoList.length > 0) {
        const result = record.relatedDealInfoList.map((item) => {
          return render.renderDictText(`${item.isDeal}`, 'yes_no');
        });
        return result.join(',');
      }
      return text;
    },
  },
  {
    title: '受让方',
    dataIndex: 'transferee',
    width: 180,
    ellipsis: true,
    customRender: ({ text, record }) => {
      if (record.relatedDealInfoList && record.relatedDealInfoList.length > 0) {
        const result = record.relatedDealInfoList.map((item) => {
          return item.transferee;
        });
        return result.join(',');
      }
      return text;
    },
  },
  {
    title: '成交价格(万元)',
    dataIndex: 'dealPrice',
    width: 130,
    align: 'right',
    customRender: ({ text, record }) => {
      if (record.relatedDealInfoList && record.relatedDealInfoList.length > 0) {
        const result = record.relatedDealInfoList.map((item) => {
          return item.dealPrice;
        });
        return result.join(',');
      }
      return text;
    },
  },
  {
    title: '挂牌开始时间',
    dataIndex: 'disclosureStartTime',
    width: 120,
  },
  {
    title: '挂牌截止时间',
    dataIndex: 'disclosureEndTime',
    width: 120,
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 150,
    ellipsis: true,
    customRender: ({ text, record }) => {
      if (record.relatedAssetsList && record.relatedAssetsList.length > 0) {
        const result = record.relatedAssetsList.map((item) => {
          return item.targetName;
        });
        return result.join(',');
      }
      return text;
    },
  },
  {
    title: '经办人',
    dataIndex: 'handlerUserName',
    width: 100,
  },
  {
    title: '录入人',
    dataIndex: 'inputUserName',
    width: 100,
  },
  {
    title: '录入时间',
    dataIndex: 'inputTime',
    width: 150,
  },
  {
    title: '内容描述',
    dataIndex: 'disclosureContent',
    width: 200,
    ellipsis: true,
    defaultHidden: true,
  },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'transferPackageName',
    label: '资产包名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产包名称',
    },
  },
  {
    field: 'transferPackageCode',
    label: '资产包编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产包编号',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择资产使用状态',
      dictCode: 'record_status',
    },
  },
  {
    field: 'isDeal',
    label: '是否成交',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择是否成交',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'manageCompany',
    label: '管理单位',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'transferCompanyName',
    label: '转让方名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入转让方名称',
    },
  },
  {
    field: 'assetName',
    label: '资产名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    field: 'assetCode',
    label: '资产编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'mainAssetType',
    label: '主要资产类型',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择资产类型',
      dictCode: 'assets_type',
    },
  },
  {
    field: 'transferMethod',
    label: '转让方式',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择转让方式',
      dictCode: 'transfer_method',
    },
  },
  {
    field: 'listingPrice',
    label: '挂牌价格范围',
    component: 'JRangeNumber',
    componentProps: {
      placeholder: ['最低价', '最高价'],
    },
  },
  {
    field: 'disclosureTime',
    label: '挂牌时间范围',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    field: 'transferee',
    label: '受让方',
    component: 'Input',
    componentProps: {
      placeholder: '请输入受让方',
    },
  },
  {
    field: 'handlerUserName',
    label: '经办人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'inputUserName',
    label: '录入人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'inputTime',
    label: '录入时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    field: 'targetName',
    label: '标的名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的名称',
    },
  },
];
